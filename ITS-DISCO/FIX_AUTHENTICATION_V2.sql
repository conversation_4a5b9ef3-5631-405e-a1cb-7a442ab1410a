-- 🔧 FIX AUTHENTICATION ISSUE V2 - Correct UUID handling
-- Run this script in your Supabase SQL Editor to fix the UUID error

-- 1. Drop existing RLS policies that require authentication
DROP POLICY IF EXISTS "Users manage their quotes" ON reference_quotes;
DROP POLICY IF EXISTS "Users view their recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "System creates recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "Users update their recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "Users upload files" ON storage.objects;
DROP POLICY IF EXISTS "Users access files" ON storage.objects;
DROP POLICY IF EXISTS "Users delete files" ON storage.objects;
DROP POLICY IF EXISTS "Allow all operations on reference_quotes" ON reference_quotes;
DROP POLICY IF EXISTS "Allow all operations on ai_recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "Allow all storage operations" ON storage.objects;

-- 2. Disable Row Level Security
ALTER TABLE reference_quotes DISABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations DISABLE ROW LEVEL SECURITY;

-- 3. Make uploaded_by field optional and use a proper UUID default
ALTER TABLE reference_quotes ALTER COLUMN uploaded_by DROP NOT NULL;
ALTER TABLE reference_quotes ALTER COLUMN uploaded_by SET DEFAULT '00000000-0000-0000-0000-000000000000';

-- 4. Create permissive policies that allow all operations
CREATE POLICY "Allow all operations on reference_quotes" ON reference_quotes FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on ai_recommendations" ON ai_recommendations FOR ALL USING (true) WITH CHECK (true);

-- 5. Create permissive storage policies
CREATE POLICY "Allow all storage operations" ON storage.objects FOR ALL USING (bucket_id = 'reference-quotes') WITH CHECK (bucket_id = 'reference-quotes');

-- 6. Grant public access to tables
GRANT ALL ON reference_quotes TO anon;
GRANT ALL ON ai_recommendations TO anon;
GRANT ALL ON storage.objects TO anon;
GRANT ALL ON storage.buckets TO anon;

-- 7. Re-enable RLS with permissive policies
ALTER TABLE reference_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;

-- Success message
SELECT '✅ Authentication requirements fixed! Reference Quote Learning System now works without login.' as fix_result;
