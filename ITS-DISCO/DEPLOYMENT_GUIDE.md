# 🚀 ITS Discovery - Digital Ocean Deployment Guide

## Quick Deployment Steps

### 1. **Prepare Your Code**
```bash
# Run the deployment preparation script
./deploy.sh
```

### 2. **Push to GitHub**
```bash
git add .
git commit -m "Prepare for Digital Ocean deployment"
git push origin main
```

### 3. **Digital Ocean App Platform Setup**

#### Option A: Using App Spec (Recommended)
1. Go to [Digital Ocean App Platform](https://cloud.digitalocean.com/apps)
2. Click "Create App"
3. Choose "GitHub" as source
4. Select your repository and branch
5. Upload the `app.yaml` file as your App Spec

#### Option B: Manual Configuration
1. **Source**: Connect your GitHub repository
2. **Build Command**: `npm run build`
3. **Run Command**: `npm run preview`
4. **Environment Variables**:
   ```
   NODE_ENV=production
   VITE_SUPABASE_URL=https://nlfmaapgqvomssugaxzw.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

### 4. **Environment Configuration**
- **Instance Size**: Basic ($5/month recommended)
- **Region**: Choose closest to your users
- **Auto-deploy**: Enable for automatic deployments

## 🔧 Troubleshooting Common Issues

### **Build Errors**

#### Memory Issues
```yaml
# In app.yaml, increase instance size
instance_size_slug: basic-xs  # Instead of basic-xxs
```

#### Node Version Issues
```yaml
# Specify Node version
environment_slug: node-js
envs:
- key: NODE_VERSION
  value: "18"
```

### **Runtime Errors**

#### Missing Environment Variables
```bash
# Check all required env vars are set in Digital Ocean dashboard
VITE_SUPABASE_URL
VITE_SUPABASE_ANON_KEY
```

#### CORS Issues
```javascript
// In Supabase dashboard, add your Digital Ocean domain to allowed origins
// Settings > API > CORS origins
https://your-app-name.ondigitalocean.app
```

### **Performance Issues**

#### Large Bundle Size
```javascript
// Already optimized in vite.config.ts with code splitting
// Monitor bundle size with:
npm run build -- --analyze
```

#### Slow Loading
```nginx
# Nginx config already includes gzip compression
# Monitor with browser dev tools
```

## 📊 Monitoring & Maintenance

### **Health Checks**
- Endpoint: `/health` (configured in nginx.conf)
- Digital Ocean will automatically monitor this

### **Logs**
```bash
# View application logs in Digital Ocean dashboard
# Runtime > Logs tab
```

### **Updates**
```bash
# Automatic deployment on git push (if enabled)
git push origin main
```

## 🔒 Security Considerations

### **Environment Variables**
- Never commit `.env` files to git
- Use Digital Ocean's environment variable management
- Rotate API keys regularly

### **HTTPS**
- Digital Ocean provides free SSL certificates
- Automatic redirect from HTTP to HTTPS

### **Headers**
- Security headers configured in nginx.conf
- CSP, XSS protection, etc.

## 💰 Cost Optimization

### **Instance Sizing**
- Start with Basic ($5/month)
- Scale up based on usage
- Monitor resource usage in dashboard

### **CDN**
- Consider Digital Ocean Spaces CDN for static assets
- Reduces bandwidth costs

## 🚨 Emergency Procedures

### **Rollback**
1. Go to Digital Ocean dashboard
2. Deployments tab
3. Click "Rollback" on previous working deployment

### **Quick Fix**
```bash
# For urgent fixes
git revert <commit-hash>
git push origin main
```

## ✅ Post-Deployment Checklist

- [ ] Application loads correctly
- [ ] All routes work (client-side routing)
- [ ] File uploads function
- [ ] Database connections work
- [ ] PDF generation works
- [ ] All environment variables set
- [ ] SSL certificate active
- [ ] Custom domain configured (if applicable)
- [ ] Monitoring alerts set up

## 📞 Support

If you encounter issues:
1. Check Digital Ocean logs
2. Verify environment variables
3. Test locally with `npm run preview`
4. Check Supabase connection
5. Review this troubleshooting guide

**Happy Deploying! 🎉**
