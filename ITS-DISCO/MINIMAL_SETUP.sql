-- 🚀 MINIMAL SETUP - Reference Quote Learning System
-- This is a simplified setup that disables <PERSON><PERSON> for easier testing
-- Run this script in your Supabase SQL Editor

-- 1. Create reference_quotes table
CREATE TABLE IF NOT EXISTS reference_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    processing_status TEXT DEFAULT 'pending',
    extracted_data JSONB,
    ai_analysis JSONB,
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    room_count INTEGER,
    square_footage INTEGER,
    complexity_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    tags TEXT[],
    team_id UUID,
    uploaded_by UUID NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create ai_recommendations table
CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    walkthrough_id UUID NOT NULL,
    recommendation_type TEXT NOT NULL,
    step_context TEXT NOT NULL,
    room_context TEXT,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    source_quotes TEXT[],
    reasoning TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    user_feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('reference-quotes', 'reference-quotes', false)
ON CONFLICT (id) DO NOTHING;

-- 4. Create basic indexes
CREATE INDEX IF NOT EXISTS idx_reference_quotes_processing_status ON reference_quotes(processing_status);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_created_at ON reference_quotes(created_at);

-- 5. Create updated_at function and triggers (safe)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop and recreate triggers safely
DROP TRIGGER IF EXISTS update_reference_quotes_updated_at ON reference_quotes;
DROP TRIGGER IF EXISTS update_ai_recommendations_updated_at ON ai_recommendations;

CREATE TRIGGER update_reference_quotes_updated_at 
    BEFORE UPDATE ON reference_quotes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_recommendations_updated_at 
    BEFORE UPDATE ON ai_recommendations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 6. DISABLE RLS for easier testing (you can enable it later)
ALTER TABLE reference_quotes DISABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations DISABLE ROW LEVEL SECURITY;

-- 7. Grant permissions
GRANT ALL ON reference_quotes TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;
GRANT ALL ON reference_quotes TO anon;
GRANT ALL ON ai_recommendations TO anon;

-- 8. Create permissive storage policies
DROP POLICY IF EXISTS "Allow all operations on reference-quotes" ON storage.objects;
CREATE POLICY "Allow all operations on reference-quotes" ON storage.objects
    FOR ALL USING (bucket_id = 'reference-quotes');

-- Success message
SELECT 'Minimal setup completed successfully! 🎉' as message,
       'RLS is disabled for easier testing. Tables and storage are ready.' as details,
       'You can now upload and manage reference quotes.' as next_steps;
