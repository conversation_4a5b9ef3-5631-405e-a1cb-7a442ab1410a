-- 🔧 SAFE Reference Quote Learning System Database Setup
-- This script can be run multiple times safely without errors
-- Copy and paste this entire script into your Supabase SQL Editor and run it

-- 1. Create reference_quotes table (safe - uses IF NOT EXISTS)
CREATE TABLE IF NOT EXISTS reference_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    extracted_data JSONB,
    ai_analysis JSONB,
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    room_count INTEGER,
    square_footage INTEGER,
    complexity_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    tags TEXT[],
    team_id UUID,
    uploaded_by UUI<PERSON> NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create ai_recommendations table (safe - uses IF NOT EXISTS)
CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    walkthrough_id UUID NOT NULL,
    recommendation_type TEXT NOT NULL CHECK (recommendation_type IN ('system_suggestion', 'product_combination', 'cost_optimization', 'configuration_option')),
    step_context TEXT NOT NULL,
    room_context TEXT,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    source_quotes TEXT[],
    reasoning TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'dismissed', 'modified')),
    user_feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create storage bucket for reference quotes (safe - uses ON CONFLICT)
INSERT INTO storage.buckets (id, name, public) 
VALUES ('reference-quotes', 'reference-quotes', false)
ON CONFLICT (id) DO NOTHING;

-- 4. Create indexes for better performance (safe - uses IF NOT EXISTS)
CREATE INDEX IF NOT EXISTS idx_reference_quotes_processing_status ON reference_quotes(processing_status);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_project_type ON reference_quotes(project_type);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_system_categories ON reference_quotes USING GIN(system_categories);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_tags ON reference_quotes USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_team_id ON reference_quotes(team_id);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_uploaded_by ON reference_quotes(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_created_at ON reference_quotes(created_at);

CREATE INDEX IF NOT EXISTS idx_ai_recommendations_walkthrough_id ON ai_recommendations(walkthrough_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_created_at ON ai_recommendations(created_at);

-- 5. Create or replace the updated_at function (safe - uses OR REPLACE)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. Create triggers with proper error handling (safe approach)
DO $$
BEGIN
    -- Drop existing triggers if they exist
    DROP TRIGGER IF EXISTS update_reference_quotes_updated_at ON reference_quotes;
    DROP TRIGGER IF EXISTS update_ai_recommendations_updated_at ON ai_recommendations;

    -- Create triggers for updated_at
    CREATE TRIGGER update_reference_quotes_updated_at
        BEFORE UPDATE ON reference_quotes
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

    CREATE TRIGGER update_ai_recommendations_updated_at
        BEFORE UPDATE ON ai_recommendations
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

EXCEPTION
    WHEN duplicate_object THEN
        -- Trigger already exists, ignore the error
        RAISE NOTICE 'Triggers already exist, skipping creation';
END $$;

-- 7. Enable Row Level Security (safe - no error if already enabled)
ALTER TABLE reference_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;

-- 8. Drop existing policies if they exist, then recreate them (safe approach)
DROP POLICY IF EXISTS "Users can view their own reference quotes" ON reference_quotes;
DROP POLICY IF EXISTS "Users can insert their own reference quotes" ON reference_quotes;
DROP POLICY IF EXISTS "Users can update their own reference quotes" ON reference_quotes;
DROP POLICY IF EXISTS "Users can delete their own reference quotes" ON reference_quotes;

-- Create RLS policies for reference_quotes
CREATE POLICY "Users can view their own reference quotes" ON reference_quotes
    FOR SELECT USING (uploaded_by = auth.uid());

CREATE POLICY "Users can insert their own reference quotes" ON reference_quotes
    FOR INSERT WITH CHECK (uploaded_by = auth.uid());

CREATE POLICY "Users can update their own reference quotes" ON reference_quotes
    FOR UPDATE USING (uploaded_by = auth.uid());

CREATE POLICY "Users can delete their own reference quotes" ON reference_quotes
    FOR DELETE USING (uploaded_by = auth.uid());

-- 9. Drop existing AI recommendation policies if they exist, then recreate them
DROP POLICY IF EXISTS "Users can view recommendations for their walkthroughs" ON ai_recommendations;
DROP POLICY IF EXISTS "System can insert recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "Users can update recommendations for their walkthroughs" ON ai_recommendations;

-- Create RLS policies for ai_recommendations
CREATE POLICY "Users can view recommendations for their walkthroughs" ON ai_recommendations
    FOR SELECT USING (
        walkthrough_id IN (
            SELECT id FROM walkthroughs WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "System can insert recommendations" ON ai_recommendations
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update recommendations for their walkthroughs" ON ai_recommendations
    FOR UPDATE USING (
        walkthrough_id IN (
            SELECT id FROM walkthroughs WHERE created_by = auth.uid()
        )
    );

-- 10. Drop existing storage policies if they exist, then recreate them
DROP POLICY IF EXISTS "Users can upload reference quote files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view reference quote files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete reference quote files" ON storage.objects;

-- Create storage policies for reference-quotes bucket
CREATE POLICY "Users can upload reference quote files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'reference-quotes'
    );

CREATE POLICY "Users can view reference quote files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'reference-quotes'
    );

CREATE POLICY "Users can delete reference quote files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'reference-quotes'
    );

-- 11. Grant necessary permissions (safe - no error if already granted)
GRANT ALL ON reference_quotes TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;

-- Success message
SELECT 'Reference Quote Learning System setup completed successfully! ✅' as message,
       'All tables, indexes, triggers, and policies have been created or updated.' as details;
