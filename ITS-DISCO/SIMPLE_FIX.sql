-- 🚀 SIMPLE FIX - Just Drop the Problematic Triggers
-- This removes the triggers that are causing errors
-- The updated_at columns will still work, just won't auto-update

-- Drop the problematic triggers
DROP TRIGGER IF EXISTS update_reference_quotes_updated_at ON reference_quotes;
DROP TRIGGER IF EXISTS update_ai_recommendations_updated_at ON ai_recommendations;

-- Verify tables exist and are accessible
SELECT 'Tables checked successfully! ✅' as message,
       COUNT(*) as reference_quotes_count
FROM reference_quotes;

-- Success message
SELECT 'Trigger errors fixed! 🎉' as status,
       'The reference quote system should now work without trigger errors.' as details,
       'You can upload and manage quotes normally.' as next_steps;
