-- 🔧 FIX AUTHENTICATION ISSUE - Remove RLS requirements for Reference Quote Learning System
-- Run this script in your Supabase SQL Editor to allow access without authentication

-- 1. Drop existing RLS policies that require authentication
DROP POLICY IF EXISTS "Users manage their quotes" ON reference_quotes;
DROP POLICY IF EXISTS "Users view their recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "System creates recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "Users update their recommendations" ON ai_recommendations;
DROP POLICY IF EXISTS "Users upload files" ON storage.objects;
DROP POLICY IF EXISTS "Users access files" ON storage.objects;
DROP POLICY IF EXISTS "Users delete files" ON storage.objects;

-- 2. Disable Row Level Security temporarily
ALTER TABLE reference_quotes DISABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations DISABLE ROW LEVEL SECURITY;

-- 3. Create permissive policies that allow all operations
CREATE POLICY "Allow all operations on reference_quotes" ON reference_quotes FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on ai_recommendations" ON ai_recommendations FOR ALL USING (true) WITH CHECK (true);

-- 4. Create permissive storage policies
CREATE POLICY "Allow all storage operations" ON storage.objects FOR ALL USING (bucket_id = 'reference-quotes') WITH CHECK (bucket_id = 'reference-quotes');

-- 5. Grant public access to tables
GRANT ALL ON reference_quotes TO anon;
GRANT ALL ON ai_recommendations TO anon;
GRANT ALL ON storage.objects TO anon;
GRANT ALL ON storage.buckets TO anon;

-- 6. Update reference_quotes table to make uploaded_by optional
ALTER TABLE reference_quotes ALTER COLUMN uploaded_by DROP NOT NULL;
ALTER TABLE reference_quotes ALTER COLUMN uploaded_by SET DEFAULT 'anonymous';

-- Success message
SELECT '✅ Authentication requirements removed! Reference Quote Learning System now works without login.' as fix_result;
