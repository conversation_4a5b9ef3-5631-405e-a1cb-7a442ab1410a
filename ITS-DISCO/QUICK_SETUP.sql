-- 🚀 REFERENCE QUOTE LEARNING SYSTEM - ONE-<PERSON><PERSON><PERSON><PERSON> SETUP
-- Simply copy this entire script and paste it into your Supabase SQL Editor, then click RUN

-- ✅ Step 1: Create reference_quotes table
CREATE TABLE IF NOT EXISTS reference_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    processing_status TEXT DEFAULT 'pending',
    extracted_data JSONB,
    ai_analysis JSONB,
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    room_count INTEGER,
    square_footage INTEGER,
    complexity_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    tags TEXT[],
    team_id UUID,
    uploaded_by UUID NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ✅ Step 2: Create ai_recommendations table
CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    walkthrough_id UUID NOT NULL,
    recommendation_type TEXT NOT NULL,
    step_context TEXT NOT NULL,
    room_context TEXT,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    source_quotes TEXT[],
    reasoning TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    user_feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ✅ Step 3: Create storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('reference-quotes', 'reference-quotes', false)
ON CONFLICT (id) DO NOTHING;

-- ✅ Step 4: Create performance indexes
CREATE INDEX IF NOT EXISTS idx_reference_quotes_status ON reference_quotes(processing_status);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_type ON reference_quotes(project_type);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_user ON reference_quotes(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_date ON reference_quotes(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_walkthrough ON ai_recommendations(walkthrough_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);

-- ✅ Step 5: Enable security
ALTER TABLE reference_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;

-- ✅ Step 6: Create security policies
CREATE POLICY "Users manage their quotes" ON reference_quotes FOR ALL USING (uploaded_by = auth.uid());
CREATE POLICY "Users view their recommendations" ON ai_recommendations FOR SELECT USING (
    walkthrough_id IN (SELECT id FROM walkthroughs WHERE created_by = auth.uid())
);
CREATE POLICY "System creates recommendations" ON ai_recommendations FOR INSERT WITH CHECK (true);
CREATE POLICY "Users update their recommendations" ON ai_recommendations FOR UPDATE USING (
    walkthrough_id IN (SELECT id FROM walkthroughs WHERE created_by = auth.uid())
);

-- ✅ Step 7: Storage policies
CREATE POLICY "Users upload files" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'reference-quotes');
CREATE POLICY "Users access files" ON storage.objects FOR SELECT USING (bucket_id = 'reference-quotes');
CREATE POLICY "Users delete files" ON storage.objects FOR DELETE USING (bucket_id = 'reference-quotes');

-- ✅ Step 8: Grant permissions
GRANT ALL ON reference_quotes TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;

-- 🎉 SUCCESS MESSAGE
SELECT '🎉 Reference Quote Learning System setup completed successfully! You can now upload quotes and receive AI recommendations.' as setup_result;
