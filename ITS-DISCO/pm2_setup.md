### PM2 Setup for Windows

1. **Install PM2 globally**:
   ```
   npm install -g pm2
   ```

2. **Create ecosystem.config.js file**:
   ```javascript
   module.exports = {
     apps: [{
       name: "its-discovery",
       script: "npm",
       args: "run preview",  // Use preview for production build
       cwd: "C:/path/to/ITS-DISCO",
       env: {
         NODE_ENV: "production",
       },
       watch: false,
       autorestart: true
     }]
   };
   ```

3. **Build the application**:
   ```
   cd C:/path/to/ITS-DISCO
   npm run build
   ```

4. **Start with PM2**:
   ```
   pm2 start ecosystem.config.js
   ```

5. **Set up PM2 to start on boot**:
   ```
   pm2 save
   pm2-startup install
   ```

6. **Monitor the application**:
   ```
   pm2 monit
   ```