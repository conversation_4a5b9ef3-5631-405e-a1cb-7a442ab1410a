-- 🧪 TEST SETUP - Verify Database Setup
-- Run this to check if your database setup is working correctly

-- Test 1: Check if tables exist
SELECT 'Testing table existence...' as test;

SELECT 
    'reference_quotes' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reference_quotes')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status;

SELECT 
    'ai_recommendations' as table_name,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ai_recommendations')
        THEN '✅ EXISTS'
        ELSE '❌ MISSING'
    END as status;

-- Test 2: Check table structure
SELECT 'Testing table structure...' as test;

SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'reference_quotes'
ORDER BY ordinal_position;

-- Test 3: Test basic operations
SELECT 'Testing basic operations...' as test;

-- Try to insert a test record
INSERT INTO reference_quotes (title, file_name, file_path, file_type, file_size, uploaded_by)
VALUES ('Test Quote', 'test.pdf', 'test/test.pdf', 'application/pdf', 1024, '00000000-0000-0000-0000-000000000000');

-- Try to select it
SELECT 'Test insert result:' as test, COUNT(*) as test_records
FROM reference_quotes 
WHERE title = 'Test Quote';

-- Try to update it
UPDATE reference_quotes 
SET processing_status = 'completed' 
WHERE title = 'Test Quote';

-- Verify update worked
SELECT 'Test update result:' as test, processing_status
FROM reference_quotes 
WHERE title = 'Test Quote';

-- Clean up test data
DELETE FROM reference_quotes WHERE title = 'Test Quote';

-- Test 4: Check storage bucket
SELECT 'Testing storage bucket...' as test;

SELECT name, public, created_at
FROM storage.buckets 
WHERE name = 'reference-quotes';

-- Test 5: Check permissions
SELECT 'Testing permissions...' as test;

SELECT 
    table_name,
    grantee,
    privilege_type
FROM information_schema.table_privileges 
WHERE table_name IN ('reference_quotes', 'ai_recommendations')
ORDER BY table_name, grantee;

-- Final result
SELECT 'Database setup test completed! 🎉' as result,
       'If you see this message, basic database operations are working.' as message;
