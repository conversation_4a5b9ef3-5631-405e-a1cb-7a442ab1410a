
import React, { Suspense } from "react";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./components/theme/ThemeProvider";
import { AuthProvider } from "./context/AuthContext";
import MainLayout from "./components/layouts/MainLayout";
import LoadingPage from "./components/ui/loading-page";

// Lazy load all page components for better code splitting
const Index = React.lazy(() => import("./pages/Index"));
const Settings = React.lazy(() => import("./pages/Settings"));
const CostManager = React.lazy(() => import("./pages/CostManager"));
const NotFound = React.lazy(() => import("./pages/NotFound"));
const Auth = React.lazy(() => import("./pages/Auth"));
const ClientsList = React.lazy(() => import("./pages/ClientsList"));
const WalkthroughPage = React.lazy(() => import("./pages/WalkthroughPage"));
const Reports = React.lazy(() => import("./pages/Reports"));
const ZapierIntegration = React.lazy(() => import("./pages/ZapierIntegration"));
const Actions = React.lazy(() => import("./pages/Actions"));
const Profile = React.lazy(() => import("./pages/Profile"));
const Products = React.lazy(() => import("./pages/Products"));
const SubcategoryManagement = React.lazy(() => import("./pages/SubcategoryManagement"));
const CategoryManagement = React.lazy(() => import("./pages/CategoryManagement"));
const ReferenceQuoteManager = React.lazy(() => import("./pages/ReferenceQuoteManager"));

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="system" storageKey="innovative-client-discovery-theme">
      <TooltipProvider>
        <Sonner />
        <AuthProvider>
          <BrowserRouter>
            <Suspense fallback={<LoadingPage message="Loading application..." />}>
              <Routes>
                <Route path="/auth" element={<Auth />} />
                <Route element={<MainLayout />}>
                  <Route path="/" element={<Index />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/cost-manager" element={<CostManager />} />
                  {/* Consolidated walkthrough routes */}
                  <Route path="/walkthrough" element={<WalkthroughPage />} />
                  <Route path="/walkthrough/:id" element={<WalkthroughPage />} />
                  {/* Legacy routes for backward compatibility */}
                  <Route path="/new-walkthrough" element={<WalkthroughPage />} />
                  <Route path="/edit-walkthrough/:id" element={<WalkthroughPage />} />
                  <Route path="/clients" element={<ClientsList />} />
                  <Route path="/reports" element={<Reports />} />
                  <Route path="/zapier" element={<ZapierIntegration />} />
                  <Route path="/actions" element={<Actions />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/products" element={<Products />} />
                  <Route path="/subcategories" element={<SubcategoryManagement />} />
                  <Route path="/categories" element={<CategoryManagement />} />
                  <Route path="/reference-quotes" element={<ReferenceQuoteManager />} />
                </Route>
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
          </BrowserRouter>
        </AuthProvider>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
