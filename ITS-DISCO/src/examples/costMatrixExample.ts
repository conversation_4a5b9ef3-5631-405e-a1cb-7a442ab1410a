/**
 * Cost Matrix Usage Examples
 * 
 * This file demonstrates how to use the new costMatrix.ts functionality
 */

import {
  calculateProjectCost,
  calculateLegacyProjectCost,
  getSystemCostEstimate,
  compareTierCosts,
  getRecommendedTier,
  formatCurrency,
  calculateFinancingOptions,
  generateCostReport,
  qualityTiers,
  costMatrixCategories
} from '@/data/costMatrix';

// Example 1: Basic cost calculation
export const basicCostExample = () => {
  const selectedSystems = {
    lighting: true,
    audio: true,
    video: true,
    network: true
  };

  const result = calculateProjectCost(selectedSystems, 'standard', 3000);
  
  console.log('=== Basic Cost Calculation ===');
  console.log(`Total Cost: ${formatCurrency(result.totalCost)}`);
  console.log(`Cost per sq ft: ${formatCurrency(result.summary.costPerSqft)}`);
  console.log('System Breakdown:');
  
  Object.entries(result.breakdown).forEach(([systemId, breakdown]) => {
    console.log(`  ${breakdown.systemName}: ${formatCurrency(breakdown.totalCost)}`);
    console.log(`    Materials: ${formatCurrency(breakdown.materialCost)}`);
    console.log(`    Labor: ${formatCurrency(breakdown.laborCost)}`);
    console.log(`    Complexity: ${breakdown.complexity}`);
  });
  
  return result;
};

// Example 2: Advanced cost calculation with options
export const advancedCostExample = () => {
  const selectedSystems = {
    lighting: true,
    shades: true,
    audio: true,
    video: true,
    network: true,
    security: true,
    controlSystems: true
  };

  const options = {
    markupPercentage: 20,
    taxRate: 9.5,
    regionalMultiplier: 1.15, // Northeast pricing
    includeProjectManagement: true
  };

  const result = calculateProjectCost(selectedSystems, 'premium', 4500, options);
  
  console.log('\n=== Advanced Cost Calculation ===');
  console.log(`Total Cost: ${formatCurrency(result.totalCost)}`);
  console.log(`Materials: ${formatCurrency(result.summary.totalMaterials)}`);
  console.log(`Labor: ${formatCurrency(result.summary.totalLabor)}`);
  console.log(`Markup: ${formatCurrency(result.summary.markup)}`);
  console.log(`Tax: ${formatCurrency(result.summary.tax)}`);
  
  return result;
};

// Example 3: Compare tiers
export const compareTiersExample = () => {
  const selectedSystems = {
    lighting: true,
    audio: true,
    video: true,
    network: true
  };

  const comparison = compareTierCosts(selectedSystems, 2500);
  
  console.log('\n=== Tier Comparison ===');
  Object.entries(comparison).forEach(([tier, result]) => {
    const tierInfo = qualityTiers[tier];
    console.log(`${tierInfo.label} (${tier}): ${formatCurrency(result.totalCost)}`);
    console.log(`  ${tierInfo.description}`);
    console.log(`  Cost per sq ft: ${formatCurrency(result.summary.costPerSqft)}`);
  });
  
  return comparison;
};

// Example 4: Get recommended tier based on budget
export const budgetRecommendationExample = () => {
  const selectedSystems = {
    lighting: true,
    audio: true,
    video: true,
    network: true,
    security: true
  };

  const budgets = [25000, 40000, 60000, 80000];
  
  console.log('\n=== Budget Recommendations ===');
  budgets.forEach(budget => {
    const recommendedTier = getRecommendedTier(selectedSystems, 3000, budget);
    const tierInfo = qualityTiers[recommendedTier];
    console.log(`Budget: ${formatCurrency(budget)} → Recommended: ${tierInfo.label} (${recommendedTier})`);
  });
};

// Example 5: Single system cost estimate
export const singleSystemExample = () => {
  console.log('\n=== Single System Estimates ===');
  
  costMatrixCategories.forEach(category => {
    const estimate = getSystemCostEstimate(category.id, 'standard', 3000);
    if (estimate) {
      console.log(`${category.name}: ${formatCurrency(estimate.totalCost)}`);
      console.log(`  Complexity: ${estimate.complexity}`);
    }
  });
};

// Example 6: Financing calculation
export const financingExample = () => {
  const totalCost = 45000;
  const financing = calculateFinancingOptions(totalCost, 20, 6.5, 5);
  
  console.log('\n=== Financing Options ===');
  console.log(`Total Cost: ${formatCurrency(totalCost)}`);
  console.log(`Down Payment (20%): ${formatCurrency(financing.downPayment)}`);
  console.log(`Loan Amount: ${formatCurrency(financing.loanAmount)}`);
  console.log(`Monthly Payment: ${formatCurrency(financing.monthlyPayment)}`);
  console.log(`Total Interest: ${formatCurrency(financing.totalInterest)}`);
  
  return financing;
};

// Example 7: Generate cost report
export const costReportExample = () => {
  const selectedSystems = {
    lighting: true,
    audio: true,
    video: true,
    network: true,
    controlSystems: true
  };

  const result = calculateProjectCost(selectedSystems, 'standard', 3500);
  const report = generateCostReport(result, 'John Smith', 'Smart Home Installation');
  
  console.log('\n=== Cost Report ===');
  console.log(`Title: ${report.title}`);
  console.log(`Summary: ${report.summary}`);
  console.log('Details:');
  report.details.forEach(detail => console.log(`  ${detail}`));
  console.log('Recommendations:');
  report.recommendations.forEach(rec => console.log(`  • ${rec}`));
  
  return report;
};

// Example 8: Legacy compatibility
export const legacyCompatibilityExample = () => {
  const selectedSystems = {
    lighting: true,
    audio: true,
    video: true
  };

  // Old way (still works)
  const legacyResult = calculateLegacyProjectCost(selectedSystems, 'standard', 3000);
  
  console.log('\n=== Legacy Compatibility ===');
  console.log(`Legacy Total: ${formatCurrency(legacyResult.totalCost)}`);
  console.log('Legacy Breakdown:');
  Object.entries(legacyResult.breakdown).forEach(([system, cost]) => {
    console.log(`  ${system}: ${formatCurrency(cost)}`);
  });
  
  return legacyResult;
};

// Run all examples
export const runAllExamples = () => {
  console.log('🏠 ITS Discovery Cost Matrix Examples\n');
  
  basicCostExample();
  advancedCostExample();
  compareTiersExample();
  budgetRecommendationExample();
  singleSystemExample();
  financingExample();
  costReportExample();
  legacyCompatibilityExample();
  
  console.log('\n✅ All examples completed!');
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).costMatrixExamples = {
    runAllExamples,
    basicCostExample,
    advancedCostExample,
    compareTiersExample,
    budgetRecommendationExample,
    singleSystemExample,
    financingExample,
    costReportExample,
    legacyCompatibilityExample
  };
}
