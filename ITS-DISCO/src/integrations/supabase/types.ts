export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      product_catalog_items: {
        Row: {
          created_at: string
          description: string | null
          discontinued: boolean | null
          document_name: string | null
          document_url: string | null
          ean: string | null
          id: string
          image_url: string | null
          is_default: boolean | null
          manufacturer: string | null
          manufacturer_sku: string | null
          model: string | null
          msrp: number | null
          name: string
          quantity: number
          room_type: string | null
          sku_number: string | null
          subcategories: string[] | null
          system_category: string
          trade_price: number | null
          unit: string
          unit_cost: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          discontinued?: boolean | null
          document_name?: string | null
          document_url?: string | null
          ean?: string | null
          id?: string
          image_url?: string | null
          is_default?: boolean | null
          manufacturer?: string | null
          manufacturer_sku?: string | null
          model?: string | null
          msrp?: number | null
          name: string
          quantity?: number
          room_type?: string | null
          sku_number?: string | null
          subcategories?: string[] | null
          system_category: string
          trade_price?: number | null
          unit?: string
          unit_cost?: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          discontinued?: boolean | null
          document_name?: string | null
          document_url?: string | null
          ean?: string | null
          id?: string
          image_url?: string | null
          is_default?: boolean | null
          manufacturer?: string | null
          manufacturer_sku?: string | null
          model?: string | null
          msrp?: number | null
          name?: string
          quantity?: number
          room_type?: string | null
          sku_number?: string | null
          subcategories?: string[] | null
          system_category?: string
          trade_price?: number | null
          unit?: string
          unit_cost?: number
          updated_at?: string
        }
        Relationships: []
      }
      product_categories: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          subcategories: string[] | null
          system_category: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          subcategories?: string[] | null
          system_category: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          subcategories?: string[] | null
          system_category?: string
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          company: string | null
          created_at: string
          first_name: string | null
          id: string
          last_name: string | null
          phone: string | null
          title: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          first_name?: string | null
          id: string
          last_name?: string | null
          phone?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          company?: string | null
          created_at?: string
          first_name?: string | null
          id?: string
          last_name?: string | null
          phone?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      team_members: {
        Row: {
          created_at: string
          id: string
          role: string
          team_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role: string
          team_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: string
          team_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      teams: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          owner_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          owner_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          owner_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      walkthrough_collaborators: {
        Row: {
          created_at: string
          id: string
          permission: string
          updated_at: string
          user_id: string
          walkthrough_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          permission: string
          updated_at?: string
          user_id: string
          walkthrough_id: string
        }
        Update: {
          created_at?: string
          id?: string
          permission?: string
          updated_at?: string
          user_id?: string
          walkthrough_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "walkthrough_collaborators_walkthrough_id_fkey"
            columns: ["walkthrough_id"]
            isOneToOne: false
            referencedRelation: "walkthroughs"
            referencedColumns: ["id"]
          },
        ]
      }
      walkthrough_comments: {
        Row: {
          content: string
          created_at: string
          id: string
          resolved: boolean
          step_index: number | null
          updated_at: string
          user_id: string
          walkthrough_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          resolved?: boolean
          step_index?: number | null
          updated_at?: string
          user_id: string
          walkthrough_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          resolved?: boolean
          step_index?: number | null
          updated_at?: string
          user_id?: string
          walkthrough_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "walkthrough_comments_walkthrough_id_fkey"
            columns: ["walkthrough_id"]
            isOneToOne: false
            referencedRelation: "walkthroughs"
            referencedColumns: ["id"]
          },
        ]
      }
      product_packages: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          final_price: number | null
          id: string
          is_default: boolean | null
          markup_percentage: number | null
          name: string
          subcategories: string[] | null
          system_category: string
          team_id: string | null
          total_cost: number | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          final_price?: number | null
          id?: string
          is_default?: boolean | null
          markup_percentage?: number | null
          name: string
          subcategories?: string[] | null
          system_category: string
          team_id?: string | null
          total_cost?: number | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          final_price?: number | null
          id?: string
          is_default?: boolean | null
          markup_percentage?: number | null
          name?: string
          subcategories?: string[] | null
          system_category?: string
          team_id?: string | null
          total_cost?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_packages_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_packages_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      product_package_items: {
        Row: {
          created_at: string
          custom_price: number | null
          id: string
          notes: string | null
          package_id: string
          product_id: string
          quantity: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          custom_price?: number | null
          id?: string
          notes?: string | null
          package_id: string
          product_id: string
          quantity?: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          custom_price?: number | null
          id?: string
          notes?: string | null
          package_id?: string
          product_id?: string
          quantity?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_package_items_package_id_fkey"
            columns: ["package_id"]
            isOneToOne: false
            referencedRelation: "product_packages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_package_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "product_catalog_items"
            referencedColumns: ["id"]
          },
        ]
      }
      reference_quotes: {
        Row: {
          id: string
          title: string
          file_name: string
          file_path: string
          file_type: string
          file_size: number
          upload_date: string
          processed_date: string | null
          processing_status: 'pending' | 'processing' | 'completed' | 'failed'
          extracted_data: Json | null
          ai_analysis: Json | null
          project_type: string | null
          estimated_budget: number | null
          system_categories: string[] | null
          room_count: number | null
          square_footage: number | null
          complexity_score: number | null
          confidence_score: number | null
          tags: string[] | null
          team_id: string | null
          uploaded_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          file_name: string
          file_path: string
          file_type: string
          file_size: number
          upload_date?: string
          processed_date?: string | null
          processing_status?: 'pending' | 'processing' | 'completed' | 'failed'
          extracted_data?: Json | null
          ai_analysis?: Json | null
          project_type?: string | null
          estimated_budget?: number | null
          system_categories?: string[] | null
          room_count?: number | null
          square_footage?: number | null
          complexity_score?: number | null
          confidence_score?: number | null
          tags?: string[] | null
          team_id?: string | null
          uploaded_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          file_name?: string
          file_path?: string
          file_type?: string
          file_size?: number
          upload_date?: string
          processed_date?: string | null
          processing_status?: 'pending' | 'processing' | 'completed' | 'failed'
          extracted_data?: Json | null
          ai_analysis?: Json | null
          project_type?: string | null
          estimated_budget?: number | null
          system_categories?: string[] | null
          room_count?: number | null
          square_footage?: number | null
          complexity_score?: number | null
          confidence_score?: number | null
          tags?: string[] | null
          team_id?: string | null
          uploaded_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "reference_quotes_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reference_quotes_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      ai_recommendations: {
        Row: {
          id: string
          walkthrough_id: string
          recommendation_type: 'system_suggestion' | 'product_combination' | 'cost_optimization' | 'configuration_option'
          step_context: string
          room_context: string | null
          recommendation_data: Json
          confidence_score: number
          source_quotes: string[] | null
          reasoning: string
          status: 'pending' | 'accepted' | 'dismissed' | 'modified'
          user_feedback: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          walkthrough_id: string
          recommendation_type: 'system_suggestion' | 'product_combination' | 'cost_optimization' | 'configuration_option'
          step_context: string
          room_context?: string | null
          recommendation_data: Json
          confidence_score: number
          source_quotes?: string[] | null
          reasoning: string
          status?: 'pending' | 'accepted' | 'dismissed' | 'modified'
          user_feedback?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          walkthrough_id?: string
          recommendation_type?: 'system_suggestion' | 'product_combination' | 'cost_optimization' | 'configuration_option'
          step_context?: string
          room_context?: string | null
          recommendation_data?: Json
          confidence_score?: number
          source_quotes?: string[] | null
          reasoning?: string
          status?: 'pending' | 'accepted' | 'dismissed' | 'modified'
          user_feedback?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_recommendations_walkthrough_id_fkey"
            columns: ["walkthrough_id"]
            isOneToOne: false
            referencedRelation: "walkthroughs"
            referencedColumns: ["id"]
          }
        ]
      }
      walkthroughs: {
        Row: {
          client_name: string
          created_at: string
          created_by: string | null
          form_data: Json
          id: string
          last_step: number
          team_id: string | null
          title: string
          updated_at: string
        }
        Insert: {
          client_name: string
          created_at?: string
          created_by?: string | null
          form_data: Json
          id?: string
          last_step?: number
          team_id?: string | null
          title: string
          updated_at?: string
        }
        Update: {
          client_name?: string
          created_at?: string
          created_by?: string | null
          form_data?: Json
          id?: string
          last_step?: number
          team_id?: string | null
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "walkthroughs_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
