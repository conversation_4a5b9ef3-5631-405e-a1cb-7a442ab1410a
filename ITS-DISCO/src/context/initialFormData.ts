
import { FormData } from "../types/formTypes";

// Initial form state
export const initialFormData: FormData = {
  clientInfo: {
    firstName: "",
    lastName: "",
    fullName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    architectInfo: "",
    homeType: "",
    projectType: "",
    company: "",
  },
  qualificationInfo: {
    budget: 50000,
    timeline: "",
    purpose: "",
    projectStage: "",
    completionDate: null,
    squareFootage: 0,
    floors: 1,
    generatedScopeOfWork: "",
  },
  systemCategories: {
    lighting: false,
    shades: false,
    audio: false,
    video: false,
    network: false,
    security: false,
    controlSystems: false,
  },
  rooms: [],
  projectFiles: {
    floorplans: [],
    references: [],
  },
  currentStep: 1, // Start with qualification step instead of client info
  walkthroughType: 'residential', // Default to residential
  executiveSummary: "",
  systemRecommendations: "",
};
