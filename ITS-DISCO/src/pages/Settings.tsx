import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getOpenAIKey, saveOpenAIKey, clearOpenAIKey } from "@/services/openaiService";
import { getZapierWebhookUrl, saveZapierWebhookUrl, clearZapierWebhookUrl } from "@/services/zapierService";
import { useTheme } from "@/components/theme/ThemeProvider";
import { useToast } from "@/hooks/use-toast";
import { useA<PERSON> } from "@/context/AuthContext";
import { Eye, EyeOff, Save, Trash, Database, Zap, Webhook, Settings as SettingsIcon, Package, Filter, Upload, Download, Brain, User, Mail, Phone, Building } from "lucide-react";
import { handleOAuthRedirect } from "@/services/zohoOAuthService";
import { Link, useNavigate } from "react-router-dom";
import ZohoOAuthConfig from "@/components/integrations/ZohoOAuthConfig";
import ImportCSVDialog from "@/components/integrations/packages/ImportCSVDialog";
import usePackageManagement from "@/hooks/usePackageManagement";
import { supabase } from "@/integrations/supabase/client";

const Settings = () => {
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [apiKey, setApiKey] = useState("");
  const [webhookUrl, setWebhookUrl] = useState("");
  const [showApiKey, setShowApiKey] = useState(false);
  const [oauthSuccess, setOauthSuccess] = useState(false);

  // User profile state
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [company, setCompany] = useState("");
  const [profileLoading, setProfileLoading] = useState(false);

  // CSV Import functionality
  const {
    isImportCSVDialogOpen,
    setIsImportCSVDialogOpen,
    handleImportCSVContent
  } = usePackageManagement();

  useEffect(() => {
    const savedKey = getOpenAIKey();
    setApiKey(savedKey);

    const savedWebhook = getZapierWebhookUrl();
    setWebhookUrl(savedWebhook);

    // Load user profile data
    if (user) {
      const loadProfileData = async () => {
        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('first_name, last_name, phone, company')
            .eq('id', user.id)
            .single();

          if (error && error.code !== 'PGRST116') {
            console.error('Error loading profile:', error);
            return;
          }

          if (data) {
            setFirstName(data.first_name || "");
            setLastName(data.last_name || "");
            setPhone(data.phone || "");
            setCompany(data.company || "");
          }

          // Always set email from the authenticated user object
          setEmail(user.email || "");
        } catch (error) {
          console.error("Error loading profile:", error);
        }
      };

      loadProfileData();
    }

    // Handle OAuth redirect if needed
    const checkOAuthRedirect = async () => {
      try {
        const success = await handleOAuthRedirect();
        if (success) {
          setOauthSuccess(true);
          toast({
            title: "Connected to Zoho CRM",
            description: "OAuth authentication successful. You can now use Zoho CRM integration.",
          });
        }
      } catch (error) {
        console.error("OAuth error:", error);
        toast({
          title: "OAuth Error",
          description: error instanceof Error ? error.message : "Failed to authenticate with Zoho CRM",
          variant: "destructive",
        });
      }
    };

    checkOAuthRedirect();
  }, [toast, user]);

  const handleSaveApiKey = () => {
    saveOpenAIKey(apiKey);
    toast({
      title: "API Key Saved",
      description: "Your OpenAI API key has been saved",
    });
  };

  const handleClearApiKey = () => {
    setApiKey("");
    clearOpenAIKey();
    toast({
      title: "API Key Removed",
      description: "Your OpenAI API key has been removed",
    });
  };

  const handleSaveWebhookUrl = () => {
    saveZapierWebhookUrl(webhookUrl);
    toast({
      title: "Webhook URL Saved",
      description: "Your Zapier webhook URL has been saved for all integrations",
    });
  };

  const handleClearWebhookUrl = () => {
    setWebhookUrl("");
    clearZapierWebhookUrl();
    toast({
      title: "Webhook URL Removed",
      description: "Your Zapier webhook URL has been removed",
    });
  };

  const handleUpdateProfile = async () => {
    if (!user) return;

    try {
      setProfileLoading(true);
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName,
          last_name: lastName,
          phone: phone,
          company: company,
        })
        .eq('id', user.id);

      if (error) throw error;

      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Update Failed",
        description: "There was an error updating your profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setProfileLoading(false);
    }
  };

  const getUserInitials = () => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    if (user?.email) {
      return user.email.substring(0, 2).toUpperCase();
    }
    return 'U';
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Settings</h1>

      <Tabs defaultValue="ai" className="max-w-3xl">
        <TabsList className="grid grid-cols-5">
          <TabsTrigger value="ai">AI / OpenAI</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="ai">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                AI / OpenAI Configuration
              </CardTitle>
              <CardDescription>
                Configure your OpenAI API key to enable AI-powered features like Executive Summary, room recommendations, and cost analysis.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Label htmlFor="openai-key" className="text-base font-medium">OpenAI API Key</Label>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="h-8 w-8"
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Input
                    id="openai-key"
                    type={showApiKey ? "text" : "password"}
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="sk-..."
                    className="font-mono text-sm"
                  />
                  <Button onClick={handleSaveApiKey} disabled={!apiKey.trim()}>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleClearApiKey}
                    disabled={!apiKey}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Clear
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Your API key is stored locally in your browser and is used for:
                </p>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Executive Summary generation</li>
                  <li>• Room recommendations and system analysis</li>
                  <li>• Cost analysis and budget breakdowns</li>
                  <li>• Audio transcription and qualification extraction</li>
                </ul>
                <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Need an API key?</strong> Visit{' '}
                    <a
                      href="https://platform.openai.com/api-keys"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="underline hover:no-underline"
                    >
                      OpenAI's API Keys page
                    </a>{' '}
                    to create one. Make sure to add billing information to your OpenAI account.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="account" className="space-y-6">
          {/* User Profile Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Profile
              </CardTitle>
              <CardDescription>
                Manage your personal information and account details.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Avatar Section */}
              <div className="flex items-center gap-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback className="text-lg font-semibold">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <h3 className="text-lg font-medium">
                    {firstName && lastName ? `${firstName} ${lastName}` : 'User Profile'}
                  </h3>
                  <p className="text-sm text-muted-foreground">{email}</p>
                  <p className="text-xs text-muted-foreground">
                    Member since {new Date(user?.created_at || '').toLocaleDateString()}
                  </p>
                </div>
              </div>

              {/* Profile Form */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="Enter your first name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lastName" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Enter your last name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    disabled
                    className="bg-muted"
                  />
                  <p className="text-xs text-muted-foreground">
                    Email cannot be changed. Contact support if needed.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="Enter your phone number"
                  />
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="company" className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Company
                  </Label>
                  <Input
                    id="company"
                    value={company}
                    onChange={(e) => setCompany(e.target.value)}
                    placeholder="Enter your company name"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleUpdateProfile}
                disabled={profileLoading}
                className="w-full md:w-auto"
              >
                <Save className="h-4 w-4 mr-2" />
                {profileLoading ? "Updating..." : "Update Profile"}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Appearance Settings</CardTitle>
              <CardDescription>
                Customize the appearance of the application.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <Label>Theme Mode</Label>
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    variant={theme === "light" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("light")}
                  >
                    Light
                  </Button>
                  <Button
                    variant={theme === "dark" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("dark")}
                  >
                    Dark
                  </Button>
                  <Button
                    variant={theme === "system" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("system")}
                  >
                    System
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <Label>Theme Variations</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <Button
                    variant={theme === "neon" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("neon")}
                  >
                    Neon
                  </Button>
                  <Button
                    variant={theme === "midnight" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("midnight")}
                  >
                    Midnight
                  </Button>
                  <Button
                    variant={theme === "oceanic" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("oceanic")}
                  >
                    Oceanic
                  </Button>
                  <Button
                    variant={theme === "cyborg" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("cyborg")}
                  >
                    Cyborg
                  </Button>
                  <Button
                    variant={theme === "aurora" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("aurora")}
                  >
                    Aurora
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="animations" defaultChecked />
                <Label htmlFor="animations">Enable animations</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data">
          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
              <CardDescription>
                Manage your application data, subcategories, and product organization.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Subcategory Management */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                    <Filter className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Subcategory Management</h3>
                    <p className="text-sm text-muted-foreground">
                      Manage default and custom subcategories for product organization
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/subcategories')}
                    className="flex items-center gap-2"
                  >
                    <SettingsIcon className="h-4 w-4" />
                    Manage
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Control which subcategories appear in dropdowns and filters. Create custom subcategories for your specific needs.
                </div>
              </div>

              {/* Category Management */}
              <div className="space-y-4 border-t pt-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
                    <Filter className="h-5 w-5 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Category Management</h3>
                    <p className="text-sm text-muted-foreground">
                      Manage product categories and system classifications
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/categories')}
                    className="flex items-center gap-2"
                  >
                    <Filter className="h-4 w-4" />
                    Manage
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Create, edit, and organize product categories. Map subcategories to system types.
                </div>
              </div>

              {/* Reference Quote Learning System */}
              <div className="space-y-4 border-t pt-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                    <Brain className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Reference Quote Learning System</h3>
                    <p className="text-sm text-muted-foreground">
                      Upload reference quotes to enhance AI recommendations and cost estimation
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/reference-quotes')}
                    className="flex items-center gap-2"
                  >
                    <Brain className="h-4 w-4" />
                    Manage
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Upload previous project quotes in various formats (PDF, Excel, CSV, Images) for AI analysis and pattern recognition.
                </div>
              </div>

              {/* Product Data */}
              <div className="space-y-4 border-t pt-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                    <Package className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Product Catalog</h3>
                    <p className="text-sm text-muted-foreground">
                      Manage your product catalog and import/export data
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsImportCSVDialogOpen(true)}
                      className="flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      Import CSV
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => navigate('/products')}
                      className="flex items-center gap-2"
                    >
                      <Package className="h-4 w-4" />
                      Manage
                    </Button>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Import products from CSV files or manage your complete product catalog. Export functionality coming soon.
                </div>
              </div>

              {/* Data Export/Import */}
              <div className="space-y-4 border-t pt-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
                    <Database className="h-5 w-5 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Data Export & Import</h3>
                    <p className="text-sm text-muted-foreground">
                      Export your data or import from external sources
                    </p>
                  </div>
                  <Button variant="outline" disabled>
                    Coming Soon
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Export walkthroughs, reports, and product data. Import from various file formats.
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations">
          <Card>
            <CardHeader>
              <CardTitle>API Integrations</CardTitle>
              <CardDescription>
                Connect your application with third-party services.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Zapier Integration - Updated Section */}
              <div className="space-y-2 border-t pt-6">
                <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  Zapier Integration
                </h3>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="zapier-webhook">Global Webhook URL</Label>
                  </div>
                  <div className="flex gap-2">
                    <Input
                      id="zapier-webhook"
                      type="text"
                      value={webhookUrl}
                      onChange={(e) => setWebhookUrl(e.target.value)}
                      placeholder="https://hooks.zapier.com/hooks/catch/..."
                      className="flex-1"
                    />
                  </div>
                  <div className="flex justify-between mt-2">
                    <Button variant="outline" size="sm" onClick={handleClearWebhookUrl} className="flex items-center gap-1">
                      <Trash className="h-3.5 w-3.5" />
                      Clear
                    </Button>
                    <Button size="sm" onClick={handleSaveWebhookUrl} className="flex items-center gap-1">
                      <Save className="h-3.5 w-3.5" />
                      Save Webhook URL
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    This webhook URL will be used by default for all Zapier integrations
                  </p>
                </div>

                <div className="mt-4">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => navigate('/zapier')}
                  >
                    <Webhook className="h-4 w-4" />
                    Configure Advanced Zapier Settings
                  </Button>
                </div>
              </div>

              {/* Zoho CRM OAuth Integration */}
              <div className="space-y-2 border-t pt-6">
                <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
                  <Database className="h-5 w-5 text-rose-600" />
                  Zoho CRM Integration
                </h3>

                {oauthSuccess && (
                  <div className="rounded-md bg-green-50 p-4 mb-4 border border-green-200">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-green-800">Successfully Connected</h3>
                        <div className="mt-2 text-sm text-green-700">
                          <p>Your application is now connected to Zoho CRM via OAuth.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <ZohoOAuthConfig />
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Connected Services</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Switch id="weQuote" defaultChecked />
                      <Label htmlFor="weQuote">WeQuote Integration</Label>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Import CSV Dialog */}
      <ImportCSVDialog
        open={isImportCSVDialogOpen}
        onOpenChange={setIsImportCSVDialogOpen}
        onImport={handleImportCSVContent}
      />
    </div>
  );
};

export default Settings;
