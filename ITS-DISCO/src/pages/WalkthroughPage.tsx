import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { PlusCircle, Building, Home, CheckCircle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage
} from "@/components/ui/breadcrumb";
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import ClientWalkthrough from '@/components/ClientWalkthrough';
import { ClientInfo } from '@/types/formTypes';
import { getWalkthroughById } from '@/services/walkthroughService';
import { initialFormData } from '@/context/initialFormData';
import { AlertCircle } from 'lucide-react';

interface WalkthroughTypeCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  selected: boolean;
  onClick: () => void;
}

const WalkthroughTypeCard: React.FC<WalkthroughTypeCardProps> = ({
  title,
  description,
  icon: Icon,
  selected,
  onClick,
}) => (
  <Card
    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
      selected
        ? 'border-primary bg-primary/5 shadow-md'
        : 'border-border hover:border-primary/50'
    }`}
    onClick={onClick}
  >
    <CardHeader className="text-center pb-2">
      <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
        <Icon className={`h-6 w-6 ${selected ? 'text-primary' : 'text-muted-foreground'}`} />
      </div>
      <CardTitle className="text-lg">{title}</CardTitle>
    </CardHeader>
    <CardContent className="pt-0">
      <CardDescription className="text-center text-sm">
        {description}
      </CardDescription>
    </CardContent>
    <CardFooter className="pt-2">
      <div className="mx-auto">
        {selected && <CheckCircle className="h-5 w-5 text-primary" />}
      </div>
    </CardFooter>
  </Card>
);

const WalkthroughPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id?: string }>();
  const [searchParams] = useSearchParams();
  const mode = searchParams.get('mode') || (id ? 'edit' : 'new');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const finishHandled = useRef(false);

  // State for new walkthrough setup
  const [walkthroughType, setWalkthroughType] = useState<'residential' | 'commercial'>('residential');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [company, setCompany] = useState('');
  const [homeType, setHomeType] = useState('');
  const [architectInfo, setArchitectInfo] = useState('');
  const [startedWalkthrough, setStartedWalkthrough] = useState(false);

  // State for edit mode
  const [walkthroughData, setWalkthroughData] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Determine if this is edit mode
  const isEditMode = mode === 'edit' && id;

  // Load walkthrough data for edit mode
  useEffect(() => {
    if (isEditMode) {
      const loadWalkthrough = async () => {
        setLoading(true);
        setError(null);
        try {
          const data = await getWalkthroughById(id);
          if (data) {
            setWalkthroughData(data);
          } else {
            setError('Walkthrough not found');
          }
        } catch (err) {
          console.error('Error loading walkthrough:', err);
          setError('Failed to load walkthrough data');
        } finally {
          setLoading(false);
        }
      };

      loadWalkthrough();
    }
  }, [isEditMode, id]);

  // Clear home/commercial type when switching walkthrough types (new mode only)
  useEffect(() => {
    if (!isEditMode) {
      if (walkthroughType === 'commercial') {
        if (homeType && ['Primary', 'Vacation', 'Rental'].includes(homeType)) {
          setHomeType('');
        }
      } else if (walkthroughType === 'residential') {
        if (homeType && ['Office', 'Retail', 'Restaurant', 'Hotel', 'Healthcare', 'Education', 'Warehouse', 'Mixed Use'].includes(homeType)) {
          setHomeType('');
        }
      }
    }
  }, [walkthroughType, isEditMode, homeType]);

  const validateForm = () => {
    if (!firstName.trim()) {
      toast({
        title: "First name is required",
        variant: "destructive",
      });
      return false;
    }
    if (!lastName.trim()) {
      toast({
        title: "Last name is required",
        variant: "destructive",
      });
      return false;
    }
    if (!email.trim()) {
      toast({
        title: "Email is required",
        variant: "destructive",
      });
      return false;
    }
    if (!homeType) {
      toast({
        title: "Please select a property type",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  const handleStart = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    const clientInfo: ClientInfo = {
      firstName,
      lastName,
      fullName: `${firstName} ${lastName}`,
      email,
      phone,
      address,
      city,
      state,
      zipCode,
      company,
      homeType,
      projectType: walkthroughType,
      architectInfo,
    };

    setStartedWalkthrough(true);
  }, [firstName, lastName, email, phone, address, city, state, zipCode, company, homeType, walkthroughType, architectInfo, validateForm]);

  const handleWalkthroughFinish = useCallback(() => {
    if (finishHandled.current) return;
    finishHandled.current = true;

    queryClient.invalidateQueries({ queryKey: ['walkthroughs'] });

    toast({
      title: "Walkthrough Completed",
      description: isEditMode 
        ? `${walkthroughData?.client_name}'s walkthrough has been updated successfully.`
        : `${firstName} ${lastName}'s walkthrough has been completed successfully.`
    });

    navigate('/clients', { replace: true });
  }, [firstName, lastName, navigate, toast, queryClient, isEditMode, walkthroughData]);

  const handleBackToClients = () => {
    navigate('/clients');
  };

  // Debug effect
  useEffect(() => {
    console.log("WalkthroughPage mounted/updated", { mode, isEditMode, startedWalkthrough });
    return () => {
      finishHandled.current = false;
    };
  }, [mode, isEditMode, startedWalkthrough]);

  // Show loading state for edit mode
  if (isEditMode && loading) {
    return (
      <div className="container mx-auto animate-fade-in max-w-4xl">
        <div className="px-3 md:px-4 py-4 md:py-6">
          <Skeleton className="h-8 w-64 mb-4" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    );
  }

  // Show error state for edit mode
  if (isEditMode && error) {
    return (
      <div className="container mx-auto animate-fade-in max-w-4xl">
        <div className="px-3 md:px-4 py-4 md:py-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={handleBackToClients} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Clients List
          </Button>
        </div>
      </div>
    );
  }

  // Render walkthrough component for edit mode or after starting new walkthrough
  if ((isEditMode && walkthroughData) || (startedWalkthrough && !isEditMode)) {
    const clientInfo = isEditMode ? undefined : {
      firstName,
      lastName,
      fullName: `${firstName} ${lastName}`,
      email,
      phone,
      address,
      city,
      state,
      zipCode,
      company,
      homeType,
      projectType: walkthroughType,
      architectInfo,
    };

    const initialData = isEditMode ? undefined : {
      ...initialFormData,
      walkthroughType,
      clientInfo,
    };

    return (
      <div className="container mx-auto animate-fade-in max-w-4xl">
        {isEditMode && (
          <div className="px-3 md:px-4 py-4 md:py-6">
            <Breadcrumb className="mb-4">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/clients">Clients List</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>
                    {walkthroughData?.client_name ? `Edit: ${walkthroughData.client_name}` : 'Client Walkthrough'}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <Button
              variant="ghost"
              className="mb-4 text-xs md:text-sm flex items-center gap-1 -ml-2 touch-manipulation"
              onClick={handleBackToClients}
            >
              <ArrowLeft className="h-3 w-3 md:h-4 md:w-4" />
              Back to Clients List
            </Button>
          </div>
        )}

        <ClientWalkthrough
          initialWalkthroughId={isEditMode ? id : undefined}
          initialClientInfo={clientInfo}
          initialWalkthroughData={isEditMode ? walkthroughData : initialData}
          onWalkthroughFinish={handleWalkthroughFinish}
          startAtQualification={!isEditMode}
        />
      </div>
    );
  }

  // Render new walkthrough setup form
  const residentialOptions = ['Primary', 'Vacation', 'Rental'];
  const commercialOptions = ['Office', 'Retail', 'Restaurant', 'Hotel', 'Healthcare', 'Education', 'Warehouse', 'Mixed Use'];
  const homeTypeOptions = walkthroughType === 'residential' ? residentialOptions : commercialOptions;

  return (
    <div className="container mx-auto py-4 md:py-8 px-3 md:px-4 animate-fade-in">
      <div className="flex items-center justify-between mb-4 md:mb-6">
        <h1 className="text-xl md:text-2xl font-bold">New Client Walkthrough</h1>
      </div>

      <div className="max-w-4xl mx-auto">
        <form
          className="bg-card rounded-lg shadow-md p-6 mb-6"
          onSubmit={handleStart}
        >
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-sm font-medium">1</span>
            Select Walkthrough Type
          </h2>
          <div className="grid md:grid-cols-2 gap-4 mb-8">
            <WalkthroughTypeCard
              title="Residential Project"
              description="For single-family homes, apartments, and other residential spaces."
              icon={Home}
              selected={walkthroughType === 'residential'}
              onClick={() => setWalkthroughType('residential')}
            />
            <WalkthroughTypeCard
              title="Commercial Project"
              description="For offices, retail spaces, restaurants, and other commercial properties."
              icon={Building}
              selected={walkthroughType === 'commercial'}
              onClick={() => setWalkthroughType('commercial')}
            />
          </div>

          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-sm font-medium">2</span>
            Client Information
          </h2>
          <div className="grid md:grid-cols-2 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="firstName">
                First Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="firstName"
                placeholder="Enter first name"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">
                Last Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="lastName"
                placeholder="Enter last name"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">
                Email <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="Enter phone number"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                placeholder="Enter street address"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                placeholder="Enter city"
                value={city}
                onChange={(e) => setCity(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="state">State</Label>
              <Input
                id="state"
                placeholder="Enter state"
                value={state}
                onChange={(e) => setState(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="zipCode">Zip Code</Label>
              <Input
                id="zipCode"
                placeholder="Enter zip code"
                value={zipCode}
                onChange={(e) => setZipCode(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
            {walkthroughType === 'commercial' && (
              <div className="space-y-2">
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  placeholder="Enter company name"
                  value={company}
                  onChange={(e) => setCompany(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="homeType">
                {walkthroughType === 'residential' ? 'Home Type' : 'Property Type'} <span className="text-red-500">*</span>
              </Label>
              <Select value={homeType} onValueChange={setHomeType} required>
                <SelectTrigger className="transition-all duration-200 focus:border-primary focus:ring-primary/20">
                  <SelectValue placeholder={`Select ${walkthroughType === 'residential' ? 'home' : 'property'} type`} />
                </SelectTrigger>
                <SelectContent>
                  {homeTypeOptions.map((option) => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="architectInfo">Architect / Designer / Builder</Label>
              <Input
                id="architectInfo"
                placeholder="Optional - Enter name or company"
                value={architectInfo}
                onChange={(e) => setArchitectInfo(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <Button
              type="submit"
              variant="gradient"
              size="lg"
              className="relative overflow-hidden font-medium transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px]"
            >
              <span className="relative z-10">Start Walkthrough</span>
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WalkthroughPage;
