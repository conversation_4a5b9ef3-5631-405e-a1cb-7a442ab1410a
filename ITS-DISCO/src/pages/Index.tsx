import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useNavigate } from 'react-router-dom';
import { FileText, Users, DollarSign, ArrowUpFromLine, Package, BarChart3 } from 'lucide-react';

const Index = () => {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Innovative Client Discovery</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
        <Card className="p-6 flex flex-col space-y-4">
          <div className="flex items-center space-x-3">
            <BarChart3 className="h-8 w-8 text-primary" />
            <h2 className="text-xl font-semibold">Dashboard Overview</h2>
          </div>
          <p className="text-muted-foreground flex-grow">
            View your project statistics, recent activity, and quick access to key features.
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/clients')}
          >
            View All Clients
          </Button>
        </Card>

        <Card className="p-6 flex flex-col space-y-4">
          <div className="flex items-center space-x-3">
            <Package className="h-8 w-8 text-primary" />
            <h2 className="text-xl font-semibold">Products</h2>
          </div>
          <p className="text-muted-foreground flex-grow">
            Browse and manage your product catalog items for different system types.
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/products')}
          >
            View Products
          </Button>
        </Card>

        <Card className="p-6 flex flex-col space-y-4">
          <div className="flex items-center space-x-3">
            <DollarSign className="h-8 w-8 text-primary" />
            <h2 className="text-xl font-semibold">Cost Manager</h2>
          </div>
          <p className="text-muted-foreground flex-grow">
            Manage project costs, create estimates, and track budgets.
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/cost-manager')}
          >
            Open Cost Manager
          </Button>
        </Card>

        <Card className="p-6 flex flex-col space-y-4">
          <div className="flex items-center space-x-3">
            <FileText className="h-8 w-8 text-primary" />
            <h2 className="text-xl font-semibold">Reports</h2>
          </div>
          <p className="text-muted-foreground flex-grow">
            Generate and view project reports, analytics, and summaries.
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/reports')}
          >
            View Reports
          </Button>
        </Card>

        <Card className="p-6 flex flex-col space-y-4">
          <div className="flex items-center space-x-3">
            <Users className="h-8 w-8 text-primary" />
            <h2 className="text-xl font-semibold">Clients List</h2>
          </div>
          <p className="text-muted-foreground flex-grow">
            View and manage your existing clients and their associated projects.
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/clients')}
          >
            View Clients List
          </Button>
        </Card>

        <Card className="p-6 flex flex-col space-y-4">
          <div className="flex items-center space-x-3">
            <ArrowUpFromLine className="h-8 w-8 text-primary" />
            <h2 className="text-xl font-semibold">Data Sync</h2>
          </div>
          <p className="text-muted-foreground flex-grow">
            Send walkthrough and customer data to external systems via Zapier integrations.
          </p>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/actions')}
          >
            Sync Data
          </Button>
        </Card>
      </div>
    </div>
  );
};

export default Index;
