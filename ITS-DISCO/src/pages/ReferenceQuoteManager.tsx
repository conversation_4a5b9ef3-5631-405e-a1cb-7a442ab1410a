// Reference Quote Manager Page

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  Brain,
  Upload,
  Search,
  Filter,
  FileText,
  Trash2,
  Eye,
  Download,
  CheckCircle,
  Clock,
  AlertCircle,
  TrendingUp,
  DollarSign,
  RefreshCw,
  PlayCircle,
  AlertTriangle,
  Zap
} from 'lucide-react';
import ReferenceQuoteUpload from '@/components/quotes/ReferenceQuoteUpload';
import SetupNotification from '@/components/quotes/SetupNotification';
import QuickSetupChecker from '@/components/quotes/QuickSetupChecker';
import {
  ReferenceQuote,
  QuoteSearchFilters,
  QuoteSearchResult
} from '@/types/referenceQuoteTypes';
import {
  searchReferenceQuotes,
  deleteReferenceQuote,
  reprocessQuote,
  bulkReprocessQuotes,
  getStuckQuotes,
  reprocessAllStuckQuotes,
  checkReferenceQuoteSetup,
  debugSupabaseConnection
} from '@/services/referenceQuoteService';

const ReferenceQuoteManager: React.FC = () => {
  const [quotes, setQuotes] = useState<ReferenceQuote[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [filters, setFilters] = useState<QuoteSearchFilters>({});
  const [selectedQuotes, setSelectedQuotes] = useState<Set<string>>(new Set());
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState('upload');
  const [needsSetup, setNeedsSetup] = useState(false);
  const [stuckQuotes, setStuckQuotes] = useState<ReferenceQuote[]>([]);
  const [reprocessingQuotes, setReprocessingQuotes] = useState<Set<string>>(new Set());
  const [bulkReprocessing, setBulkReprocessing] = useState(false);

  const { toast } = useToast();

  useEffect(() => {
    loadQuotes();
  }, [filters, searchText, currentPage]);

  // Force setup check on component mount
  useEffect(() => {
    checkSetupStatus();

    // Expose debug function globally for console access
    (window as any).debugSupabase = debugSupabaseConnection;
    (window as any).checkSetup = checkReferenceQuoteSetup;
    console.log('🔧 Debug functions available: window.debugSupabase() and window.checkSetup()');
  }, []);

  // Load stuck quotes when manage tab is active
  useEffect(() => {
    if (activeTab === 'manage' && !needsSetup) {
      loadStuckQuotes();
    }
  }, [activeTab, needsSetup]);

  const checkSetupStatus = async () => {
    try {
      console.log('🔍 Starting setup status check...');
      const setupStatus = await checkReferenceQuoteSetup();

      console.log('📋 Setup status result:', setupStatus);

      if (setupStatus.tablesExist && setupStatus.storageExists) {
        console.log('✅ Setup is complete');
        setNeedsSetup(false);
      } else {
        console.log('🚨 Setup incomplete:', setupStatus);
        setNeedsSetup(true);

        if (setupStatus.error) {
          console.error('Setup error details:', setupStatus.error);
          toast({
            title: 'Setup Required',
            description: setupStatus.error,
            variant: 'destructive'
          });
        }
      }
    } catch (error) {
      console.error('❌ Error checking setup status:', error);
      console.error('Error object:', error);
      // Assume setup is needed if we can't check
      setNeedsSetup(true);

      toast({
        title: 'Setup Check Failed',
        description: 'Unable to verify database setup. Please check console for details.',
        variant: 'destructive'
      });
    }
  };

  const loadQuotes = async () => {
    try {
      setLoading(true);
      const searchFilters: QuoteSearchFilters = {
        ...filters,
        searchText: searchText || undefined
      };

      const result: QuoteSearchResult = await searchReferenceQuotes(
        searchFilters,
        currentPage,
        20
      );

      setQuotes(result.quotes);
      setTotalCount(result.totalCount);
      setNeedsSetup(false); // Tables exist if we got here
    } catch (error) {
      console.error('Error loading quotes:', error);

      // Check if this is a setup issue
      if (error instanceof Error && error.message.includes('relation "public.reference_quotes" does not exist')) {
        setNeedsSetup(true);
        setQuotes([]);
        setTotalCount(0);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load reference quotes.',
          variant: 'destructive'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleUploadComplete = (quoteId: string) => {
    toast({
      title: 'Upload Successful',
      description: 'Quote uploaded successfully. AI analysis is in progress.',
    });
    loadQuotes(); // Refresh the list
    setActiveTab('manage'); // Switch to management tab
  };

  const handleUploadError = (error: string) => {
    toast({
      title: 'Upload Failed',
      description: error,
      variant: 'destructive'
    });
  };

  // Load stuck quotes
  const loadStuckQuotes = async () => {
    try {
      const stuck = await getStuckQuotes();
      setStuckQuotes(stuck);
    } catch (error) {
      console.error('Error loading stuck quotes:', error);
    }
  };

  // Reprocess a single quote
  const handleReprocessQuote = async (quoteId: string) => {
    try {
      setReprocessingQuotes(prev => new Set(prev).add(quoteId));

      const result = await reprocessQuote(quoteId);

      if (result.success) {
        toast({
          title: 'Quote Reprocessing Started',
          description: 'The quote has been queued for reprocessing.',
        });

        // Reload quotes to show updated status
        await loadQuotes();
        await loadStuckQuotes();
      } else {
        toast({
          title: 'Reprocessing Failed',
          description: result.error || 'Failed to reprocess quote',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An error occurred while reprocessing the quote',
        variant: 'destructive'
      });
    } finally {
      setReprocessingQuotes(prev => {
        const newSet = new Set(prev);
        newSet.delete(quoteId);
        return newSet;
      });
    }
  };

  // Reprocess all stuck quotes
  const handleReprocessAllStuck = async () => {
    try {
      setBulkReprocessing(true);

      const result = await reprocessAllStuckQuotes();

      if (result.success) {
        toast({
          title: 'Bulk Reprocessing Complete',
          description: `Successfully reprocessed ${result.processed} quotes.`,
        });
      } else {
        toast({
          title: 'Bulk Reprocessing Completed with Errors',
          description: `Processed ${result.processed} quotes, ${result.failed.length} failed.`,
          variant: 'destructive'
        });
      }

      // Reload quotes to show updated status
      await loadQuotes();
      await loadStuckQuotes();

    } catch (error) {
      toast({
        title: 'Bulk Reprocessing Failed',
        description: 'An error occurred during bulk reprocessing',
        variant: 'destructive'
      });
    } finally {
      setBulkReprocessing(false);
    }
  };

  const handleDeleteQuote = async (quoteId: string) => {
    if (!confirm('Are you sure you want to delete this quote? This action cannot be undone.')) {
      return;
    }

    try {
      const success = await deleteReferenceQuote(quoteId);
      if (success) {
        setQuotes(prev => prev.filter(q => q.id !== quoteId));
        setSelectedQuotes(prev => {
          const newSet = new Set(prev);
          newSet.delete(quoteId);
          return newSet;
        });
        toast({
          title: 'Quote Deleted',
          description: 'The reference quote has been deleted successfully.',
        });
      } else {
        throw new Error('Delete operation failed');
      }
    } catch (error) {
      console.error('Error deleting quote:', error);
      toast({
        title: 'Delete Failed',
        description: 'Failed to delete the quote. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending Analysis';
      case 'processing':
        return 'Analyzing...';
      case 'completed':
        return 'Analysis Complete';
      case 'failed':
        return 'Analysis Failed';
      default:
        return 'Unknown Status';
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reference Quote Learning System</h1>
          <p className="text-muted-foreground">
            Upload and manage reference quotes to enhance AI recommendations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Brain className="h-3 w-3" />
            AI-Powered
          </Badge>
        </div>
      </div>

      <QuickSetupChecker />

      {needsSetup ? (
        <SetupNotification onSetupComplete={() => setActiveTab('upload')} />
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Upload Quotes
            </TabsTrigger>
            <TabsTrigger value="manage" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Manage Quotes ({totalCount})
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Analytics
            </TabsTrigger>
          </TabsList>

        <TabsContent value="upload" className="space-y-6">
          <ReferenceQuoteUpload
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
          />
        </TabsContent>

        <TabsContent value="manage" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Search & Filter
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search quotes by title, file name, or project type..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Reprocessing Controls */}
          <Card className="border-l-4 border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-400">
                <RefreshCw className="h-5 w-5" />
                Quote Reprocessing
              </CardTitle>
              <CardDescription>
                Manage and reprocess quotes that failed or are stuck in processing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Stuck Quotes Section */}
              {stuckQuotes.length > 0 ? (
                <div className="p-4 border border-yellow-200 bg-yellow-50/50 dark:bg-yellow-950/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="font-medium text-yellow-700 dark:text-yellow-400">
                      {stuckQuotes.length} Stuck Quote{stuckQuotes.length !== 1 ? 's' : ''} Detected
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm text-yellow-700 dark:text-yellow-300">Affected quotes:</p>
                      <div className="flex flex-wrap gap-2">
                        {stuckQuotes.slice(0, 3).map((quote) => (
                          <Badge key={quote.id} variant="outline" className="text-xs border-yellow-300">
                            {quote.title}
                          </Badge>
                        ))}
                        {stuckQuotes.length > 3 && (
                          <Badge variant="outline" className="text-xs border-yellow-300">
                            +{stuckQuotes.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={loadStuckQuotes}
                        className="flex items-center gap-2"
                      >
                        <RefreshCw className="h-3 w-3" />
                        Refresh
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={handleReprocessAllStuck}
                        disabled={bulkReprocessing}
                        className="flex items-center gap-2 bg-yellow-600 hover:bg-yellow-700"
                      >
                        {bulkReprocessing ? (
                          <RefreshCw className="h-3 w-3 animate-spin" />
                        ) : (
                          <Zap className="h-3 w-3" />
                        )}
                        {bulkReprocessing ? 'Reprocessing...' : 'Reprocess All Stuck'}
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-green-200 bg-green-50/50 dark:bg-green-950/20 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-700 dark:text-green-400">
                      No stuck quotes detected. All quotes are processing normally.
                    </span>
                  </div>
                </div>
              )}

              {/* General Reprocessing Info */}
              <div className="text-xs text-muted-foreground p-3 bg-muted/50 rounded">
                <strong>How reprocessing works:</strong> Individual quotes can be reprocessed using the
                <PlayCircle className="h-3 w-3 inline mx-1" /> button next to each quote.
                This resets the quote to pending status and queues it for AI analysis again,
                which usually resolves issues caused by temporary processing failures or system interruptions.
              </div>
            </CardContent>
          </Card>

          {/* Quote List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Reference Quotes</CardTitle>
                  <CardDescription>
                    {totalCount} quote{totalCount !== 1 ? 's' : ''} available for AI analysis
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  {quotes.filter(q => q.processingStatus === 'pending' || q.processingStatus === 'failed').length > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const failedAndPendingQuotes = quotes.filter(q => q.processingStatus === 'pending' || q.processingStatus === 'failed');
                        failedAndPendingQuotes.forEach(quote => handleReprocessQuote(quote.id));
                      }}
                      disabled={bulkReprocessing || reprocessingQuotes.size > 0}
                      className="flex items-center gap-2 text-blue-600 border-blue-200 hover:bg-blue-50"
                    >
                      {bulkReprocessing || reprocessingQuotes.size > 0 ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Zap className="h-4 w-4" />
                      )}
                      <span className="text-xs">
                        Reprocess Failed ({quotes.filter(q => q.processingStatus === 'pending' || q.processingStatus === 'failed').length})
                      </span>
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={loadQuotes}
                    className="flex items-center gap-2"
                    title="Refresh quote list"
                  >
                    <RefreshCw className="h-4 w-4" />
                    <span className="text-xs">Refresh</span>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="text-muted-foreground mt-2">Loading quotes...</p>
                </div>
              ) : quotes.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No quotes found. Upload some reference quotes to get started.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {quotes.map((quote) => (
                    <Card key={quote.id} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-medium truncate">{quote.title}</h3>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(quote.processingStatus)}
                                <span className="text-xs text-muted-foreground">
                                  {getStatusText(quote.processingStatus)}
                                </span>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                              <span>{quote.fileName}</span>
                              <span>{formatFileSize(quote.fileSize)}</span>
                              <span>{new Date(quote.uploadDate).toLocaleDateString()}</span>
                            </div>

                            <div className="flex items-center gap-2 mb-2">
                              {quote.projectType && (
                                <Badge variant="outline">{quote.projectType}</Badge>
                              )}
                              {quote.estimatedBudget && (
                                <Badge variant="secondary" className="flex items-center gap-1">
                                  <DollarSign className="h-3 w-3" />
                                  {formatCurrency(quote.estimatedBudget)}
                                </Badge>
                              )}
                              {quote.confidenceScore && (
                                <Badge variant="outline">
                                  {Math.round(quote.confidenceScore * 100)}% confidence
                                </Badge>
                              )}
                            </div>

                            {quote.tags && quote.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1">
                                {quote.tags.map((tag) => (
                                  <Badge key={tag} variant="secondary" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2 ml-4">
                            <Button variant="ghost" size="sm" title="View quote details">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" title="Download quote file">
                              <Download className="h-4 w-4" />
                            </Button>
                            {(quote.processingStatus === 'pending' || quote.processingStatus === 'failed') && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleReprocessQuote(quote.id)}
                                disabled={reprocessingQuotes.has(quote.id)}
                                className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                                title={`Reprocess this ${quote.processingStatus} quote`}
                              >
                                {reprocessingQuotes.has(quote.id) ? (
                                  <>
                                    <RefreshCw className="h-4 w-4 animate-spin mr-1" />
                                    <span className="text-xs">Processing...</span>
                                  </>
                                ) : (
                                  <>
                                    <PlayCircle className="h-4 w-4 mr-1" />
                                    <span className="text-xs">Reprocess</span>
                                  </>
                                )}
                              </Button>
                            )}
                            {quote.processingStatus === 'completed' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleReprocessQuote(quote.id)}
                                disabled={reprocessingQuotes.has(quote.id)}
                                className="text-gray-500 hover:text-blue-600"
                                title="Reprocess this completed quote"
                              >
                                {reprocessingQuotes.has(quote.id) ? (
                                  <RefreshCw className="h-4 w-4 animate-spin" />
                                ) : (
                                  <RefreshCw className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteQuote(quote.id)}
                              className="text-red-500 hover:text-red-700"
                              title="Delete this quote"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Quotes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{totalCount}</div>
                <p className="text-muted-foreground text-sm">Reference quotes uploaded</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Processed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">
                  {quotes.filter(q => q.processingStatus === 'completed').length}
                </div>
                <p className="text-muted-foreground text-sm">Successfully analyzed</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Average Budget</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {quotes.length > 0 ? formatCurrency(
                    quotes
                      .filter(q => q.estimatedBudget)
                      .reduce((sum, q) => sum + (q.estimatedBudget || 0), 0) / 
                    quotes.filter(q => q.estimatedBudget).length || 0
                  ) : '$0'}
                </div>
                <p className="text-muted-foreground text-sm">From analyzed quotes</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Learning System Status</CardTitle>
              <CardDescription>
                The AI system continuously learns from uploaded quotes to improve recommendations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Pattern Recognition</h4>
                    <p className="text-sm text-muted-foreground">
                      Analyzing system combinations and configurations
                    </p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Cost Analysis</h4>
                    <p className="text-sm text-muted-foreground">
                      Learning pricing patterns and optimization opportunities
                    </p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">Recommendation Engine</h4>
                    <p className="text-sm text-muted-foreground">
                      Generating contextual suggestions during walkthroughs
                    </p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default ReferenceQuoteManager;
