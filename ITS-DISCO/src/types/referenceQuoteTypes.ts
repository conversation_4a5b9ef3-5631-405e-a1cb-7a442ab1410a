// Reference Quote Learning System Types

export interface ReferenceQuote {
  id: string;
  title: string;
  fileName: string;
  filePath: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
  processedDate?: string;
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  extractedData?: QuoteExtractedData;
  aiAnalysis?: QuoteAIAnalysis;
  projectType?: string;
  estimatedBudget?: number;
  systemCategories?: string[];
  roomCount?: number;
  squareFootage?: number;
  complexityScore?: number;
  confidenceScore?: number;
  tags?: string[];
  teamId?: string;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuoteExtractedData {
  // Raw extracted text and structured data
  rawText?: string;
  clientInfo?: {
    name?: string;
    address?: string;
    projectType?: string;
  };
  projectDetails?: {
    rooms?: ExtractedRoom[];
    systems?: ExtractedSystem[];
    totalCost?: number;
    laborCost?: number;
    materialCost?: number;
    timeline?: string;
    squareFootage?: number;
  };
  lineItems?: ExtractedLineItem[];
  metadata?: {
    extractionMethod: 'ocr' | 'pdf_text' | 'csv_parse' | 'excel_parse';
    extractionDate: string;
    confidence: number;
  };
}

export interface ExtractedRoom {
  name: string;
  type?: string;
  systems?: string[];
  products?: ExtractedProduct[];
  cost?: number;
  notes?: string;
}

export interface ExtractedSystem {
  category: string;
  subcategory?: string;
  products?: ExtractedProduct[];
  totalCost?: number;
  laborHours?: number;
  complexity?: 'low' | 'medium' | 'high';
}

export interface ExtractedProduct {
  name: string;
  manufacturer?: string;
  model?: string;
  sku?: string;
  quantity: number;
  unitCost?: number;
  totalCost?: number;
  category?: string;
  subcategory?: string;
}

export interface ExtractedLineItem {
  description: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  category?: string;
  isLabor?: boolean;
  room?: string;
}

export interface QuoteAIAnalysis {
  // AI-generated insights and patterns
  projectPatterns: {
    commonSystemCombinations: SystemCombination[];
    typicalRoomConfigurations: RoomConfiguration[];
    costPatterns: CostPattern[];
    complexityIndicators: ComplexityIndicator[];
  };
  recommendations: {
    suggestedSystems: string[];
    potentialUpsells: string[];
    costOptimizations: CostOptimization[];
    riskFactors: RiskFactor[];
  };
  benchmarks: {
    costPerSquareFoot: number;
    laborToMaterialRatio: number;
    systemDistribution: Record<string, number>;
    timelineEstimate: string;
  };
  confidence: {
    overallScore: number;
    dataQuality: number;
    extractionAccuracy: number;
    analysisReliability: number;
  };
  processingMetadata: {
    analysisDate: string;
    modelVersion: string;
    processingTime: number;
  };
}

export interface SystemCombination {
  systems: string[];
  frequency: number;
  averageCost: number;
  complexity: 'low' | 'medium' | 'high';
  notes?: string;
}

export interface RoomConfiguration {
  roomType: string;
  commonSystems: string[];
  averageCost: number;
  typicalProducts: ExtractedProduct[];
  complexity: 'low' | 'medium' | 'high';
}

export interface CostPattern {
  category: string;
  averageCost: number;
  costRange: { min: number; max: number };
  laborPercentage: number;
  frequency: number;
}

export interface ComplexityIndicator {
  factor: string;
  impact: 'low' | 'medium' | 'high';
  description: string;
  costMultiplier?: number;
}

export interface CostOptimization {
  description: string;
  potentialSavings: number;
  impact: 'low' | 'medium' | 'high';
  feasibility: 'easy' | 'moderate' | 'difficult';
  recommendation: string;
}

export interface RiskFactor {
  type: 'cost' | 'timeline' | 'technical' | 'scope';
  description: string;
  probability: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  mitigation?: string;
}

// AI Recommendation Types
export interface AIRecommendation {
  id: string;
  walkthroughId: string;
  recommendationType: 'system_suggestion' | 'product_combination' | 'cost_optimization' | 'configuration_option';
  stepContext: string;
  roomContext?: string;
  recommendationData: RecommendationData;
  confidenceScore: number;
  sourceQuotes?: string[];
  reasoning: string;
  status: 'pending' | 'accepted' | 'dismissed' | 'modified';
  userFeedback?: string;
  createdAt: string;
  updatedAt: string;
}

export interface RecommendationData {
  title: string;
  description: string;
  suggestedAction: string;
  alternatives?: Alternative[];
  costImpact?: {
    estimated: number;
    range: { min: number; max: number };
    confidence: number;
  };
  benefits?: string[];
  considerations?: string[];
  relatedProducts?: string[];
  implementationNotes?: string;
}

export interface Alternative {
  title: string;
  description: string;
  costDifference: number;
  pros: string[];
  cons: string[];
  confidence: number;
}

// Quote Upload and Processing Types
export interface QuoteUploadRequest {
  file: File;
  title: string;
  projectType?: string;
  tags?: string[];
  notes?: string;
}

export interface QuoteProcessingJob {
  id: string;
  quoteId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  currentStep: string;
  startedAt: string;
  completedAt?: string;
  error?: string;
  result?: QuoteExtractedData;
}

// Learning System Configuration
export interface LearningSystemConfig {
  enableRecommendations: boolean;
  confidenceThreshold: number;
  maxRecommendationsPerStep: number;
  autoProcessUploads: boolean;
  retentionPeriod: number; // days
  analysisDepth: 'basic' | 'standard' | 'comprehensive';
  notificationSettings: {
    processingComplete: boolean;
    newRecommendations: boolean;
    systemUpdates: boolean;
  };
}

// Search and Filter Types
export interface QuoteSearchFilters {
  projectType?: string;
  systemCategories?: string[];
  budgetRange?: { min: number; max: number };
  dateRange?: { start: string; end: string };
  tags?: string[];
  processingStatus?: string[];
  confidenceRange?: { min: number; max: number };
  searchText?: string;
}

export interface QuoteSearchResult {
  quotes: ReferenceQuote[];
  totalCount: number;
  facets: {
    projectTypes: { value: string; count: number }[];
    systemCategories: { value: string; count: number }[];
    tags: { value: string; count: number }[];
  };
}
