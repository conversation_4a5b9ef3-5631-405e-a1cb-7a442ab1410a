
/**
 * Cost Matrix System for ITS Discovery
 *
 * This module provides comprehensive cost estimation capabilities for home automation projects.
 * Based on industry standards and real-world project data.
 *
 * Features:
 * - Quality tier-based pricing (Good/Better/Best)
 * - Square footage scaling
 * - System-specific cost calculations
 * - Room-based cost allocation
 * - Labor and material breakdown
 * - Regional cost adjustments
 *
 * @version 2.0.0
 * <AUTHOR> Discovery Team
 */

import { SystemCategories } from '@/types/formTypes';

// ===== TYPE DEFINITIONS =====

export interface CostMatrixTier {
  name: string;
  label: string;
  description: string;
  multiplier: number;
  sqftRate: number; // Cost per square foot for this tier
  laborMultiplier: number; // Labor cost multiplier for this tier
}

export interface CostMatrixCategory {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  percentOfTotal: number;
  laborPercentage: number; // Percentage of cost that is labor
  complexityFactor: number; // Installation complexity (1.0 = standard)
  qualityTiers: {
    basic: number;
    standard: number;
    premium: number;
  };
  scalingFactors: {
    sqftThreshold: number; // Square footage where scaling begins
    scalingRate: number; // How much cost increases per additional sqft
  };
}

export interface ProjectCostResult {
  totalCost: number;
  breakdown: Record<string, SystemCostBreakdown>;
  summary: CostSummary;
}

export interface SystemCostBreakdown {
  systemName: string;
  materialCost: number;
  laborCost: number;
  totalCost: number;
  quantity: number;
  complexity: 'low' | 'medium' | 'high';
}

export interface CostSummary {
  subtotal: number;
  totalMaterials: number;
  totalLabor: number;
  markup: number;
  tax: number;
  grandTotal: number;
  costPerSqft: number;
}

export interface CostCalculationOptions {
  qualityTier: string;
  squareFootage: number;
  markupPercentage?: number;
  taxRate?: number;
  regionalMultiplier?: number;
  complexityAdjustment?: number;
  includeProjectManagement?: boolean;
}

// ===== QUALITY TIER DEFINITIONS =====

export const qualityTiers: Record<string, CostMatrixTier> = {
  basic: {
    name: "basic",
    label: "Good",
    description: "Entry level home automation with reliable basic functionality and standard components",
    multiplier: 0.75,
    sqftRate: 8,
    laborMultiplier: 0.8,
  },
  standard: {
    name: "standard",
    label: "Better",
    description: "Mid-range home automation with advanced features and quality components",
    multiplier: 1.0,
    sqftRate: 12,
    laborMultiplier: 1.0,
  },
  premium: {
    name: "premium",
    label: "Best",
    description: "Premium home automation with high-end components, custom integration, and white-glove service",
    multiplier: 1.6,
    sqftRate: 18,
    laborMultiplier: 1.4,
  }
};

// ===== REGIONAL COST MULTIPLIERS =====

export const regionalMultipliers: Record<string, number> = {
  'northeast': 1.15,
  'southeast': 0.95,
  'midwest': 0.90,
  'southwest': 1.05,
  'west': 1.25,
  'california': 1.35,
  'newyork': 1.30,
  'florida': 0.92,
  'texas': 0.98,
  'default': 1.0
};

// ===== SYSTEM CATEGORY DEFINITIONS =====

export const costMatrixCategories: CostMatrixCategory[] = [
  {
    id: "lighting",
    name: "Lighting Control",
    description: "Smart lighting systems, dimmers, switches, and automated controls",
    basePrice: 275,
    percentOfTotal: 15,
    laborPercentage: 35,
    complexityFactor: 1.0,
    qualityTiers: {
      basic: 180,
      standard: 275,
      premium: 450,
    },
    scalingFactors: {
      sqftThreshold: 2500,
      scalingRate: 0.08
    }
  },
  {
    id: "shades",
    name: "Window Treatments",
    description: "Motorized shades, blinds, and automated window coverings",
    basePrice: 850,
    percentOfTotal: 12,
    laborPercentage: 25,
    complexityFactor: 1.2,
    qualityTiers: {
      basic: 550,
      standard: 850,
      premium: 1350,
    },
    scalingFactors: {
      sqftThreshold: 3000,
      scalingRate: 0.12
    }
  },
  {
    id: "audio",
    name: "Audio Systems",
    description: "Whole-home audio, speakers, amplifiers, and streaming solutions",
    basePrice: 650,
    percentOfTotal: 20,
    laborPercentage: 30,
    complexityFactor: 1.1,
    qualityTiers: {
      basic: 425,
      standard: 650,
      premium: 1100,
    },
    scalingFactors: {
      sqftThreshold: 2000,
      scalingRate: 0.15
    }
  },
  {
    id: "video",
    name: "Video Systems",
    description: "TVs, projectors, video distribution, and entertainment systems",
    basePrice: 1350,
    percentOfTotal: 25,
    laborPercentage: 20,
    complexityFactor: 1.3,
    qualityTiers: {
      basic: 900,
      standard: 1350,
      premium: 2250,
    },
    scalingFactors: {
      sqftThreshold: 2500,
      scalingRate: 0.18
    }
  },
  {
    id: "network",
    name: "Networking",
    description: "Enterprise-grade network infrastructure, WiFi, and connectivity",
    basePrice: 450,
    percentOfTotal: 8,
    laborPercentage: 40,
    complexityFactor: 1.4,
    qualityTiers: {
      basic: 280,
      standard: 450,
      premium: 750,
    },
    scalingFactors: {
      sqftThreshold: 3000,
      scalingRate: 0.06
    }
  },
  {
    id: "security",
    name: "Security & Surveillance",
    description: "Security cameras, access control, monitoring, and alarm systems",
    basePrice: 550,
    percentOfTotal: 12,
    laborPercentage: 35,
    complexityFactor: 1.2,
    qualityTiers: {
      basic: 350,
      standard: 550,
      premium: 900,
    },
    scalingFactors: {
      sqftThreshold: 2500,
      scalingRate: 0.10
    }
  },
  {
    id: "controlSystems",
    name: "Control Systems",
    description: "Central automation hubs, interfaces, and system integration",
    basePrice: 800,
    percentOfTotal: 8,
    laborPercentage: 50,
    complexityFactor: 1.5,
    qualityTiers: {
      basic: 525,
      standard: 800,
      premium: 1350,
    },
    scalingFactors: {
      sqftThreshold: 2000,
      scalingRate: 0.05
    }
  }
];

// ===== HELPER FUNCTIONS =====

/**
 * Get the appropriate quality tier, defaulting to standard if invalid
 */
export const getValidTier = (tier: string): string => {
  return tier in qualityTiers ? tier : 'standard';
};

/**
 * Calculate square footage scaling factor
 */
export const calculateSqftScaling = (
  squareFootage: number,
  category: CostMatrixCategory
): number => {
  if (squareFootage <= category.scalingFactors.sqftThreshold) {
    return 1.0;
  }

  const excessSqft = squareFootage - category.scalingFactors.sqftThreshold;
  return 1.0 + (excessSqft * category.scalingFactors.scalingRate / 1000);
};

/**
 * Calculate complexity adjustment based on system combination
 */
export const calculateComplexityAdjustment = (
  selectedSystems: Record<string, boolean>
): number => {
  const enabledSystems = Object.values(selectedSystems).filter(Boolean).length;

  if (enabledSystems <= 2) return 1.0;
  if (enabledSystems <= 4) return 1.1;
  if (enabledSystems <= 6) return 1.2;
  return 1.3; // 7+ systems
};

// ===== MAIN CALCULATION FUNCTIONS =====

/**
 * Calculate comprehensive project cost with detailed breakdown
 */
export const calculateProjectCost = (
  selectedSystems: Record<string, boolean> | SystemCategories,
  qualityTier: string,
  squareFootage: number,
  options: Partial<CostCalculationOptions> = {}
): ProjectCostResult => {
  // Set defaults and validate inputs
  const tier = getValidTier(qualityTier);
  const tierData = qualityTiers[tier];
  const sqft = Math.max(squareFootage, 500); // Minimum 500 sqft

  // Extract options with defaults
  const {
    markupPercentage = 15,
    taxRate = 8.25,
    regionalMultiplier = 1.0,
    complexityAdjustment,
    includeProjectManagement = true
  } = options;

  // Calculate complexity adjustment if not provided
  const complexityFactor = complexityAdjustment || calculateComplexityAdjustment(selectedSystems);

  // Initialize totals
  let totalMaterials = 0;
  let totalLabor = 0;
  const breakdown: Record<string, SystemCostBreakdown> = {};

  // Calculate costs for each selected system
  Object.entries(selectedSystems).forEach(([systemId, isSelected]) => {
    if (isSelected) {
      const category = costMatrixCategories.find(cat => cat.id === systemId);
      if (category) {
        // Get base cost for this tier
        const baseCost = category.qualityTiers[tier as keyof typeof category.qualityTiers] || category.basePrice;

        // Apply square footage scaling
        const sqftScaling = calculateSqftScaling(sqft, category);

        // Calculate material and labor costs
        const scaledCost = baseCost * sqftScaling * tierData.multiplier * regionalMultiplier * complexityFactor;
        const laborCost = scaledCost * (category.laborPercentage / 100) * tierData.laborMultiplier;
        const materialCost = scaledCost - laborCost;

        // Determine complexity level
        let complexity: 'low' | 'medium' | 'high' = 'medium';
        if (category.complexityFactor <= 1.0) complexity = 'low';
        else if (category.complexityFactor >= 1.3) complexity = 'high';

        breakdown[systemId] = {
          systemName: category.name,
          materialCost,
          laborCost,
          totalCost: scaledCost,
          quantity: 1,
          complexity
        };

        totalMaterials += materialCost;
        totalLabor += laborCost;
      }
    }
  });

  // Add project management costs if enabled
  if (includeProjectManagement) {
    const pmCost = (totalMaterials + totalLabor) * 0.08; // 8% for project management
    totalLabor += pmCost;
  }

  // Calculate subtotal
  const subtotal = totalMaterials + totalLabor;

  // Apply markup
  const markup = subtotal * (markupPercentage / 100);

  // Calculate tax
  const tax = (subtotal + markup) * (taxRate / 100);

  // Calculate grand total
  const grandTotal = subtotal + markup + tax;

  // Create summary
  const summary: CostSummary = {
    subtotal,
    totalMaterials,
    totalLabor,
    markup,
    tax,
    grandTotal,
    costPerSqft: grandTotal / sqft
  };

  return {
    totalCost: grandTotal,
    breakdown,
    summary
  };
};

/**
 * Legacy function for backward compatibility
 * @deprecated Use calculateProjectCost instead
 */
export const calculateLegacyProjectCost = (
  selectedSystems: Record<string, boolean> | SystemCategories,
  qualityTier: string,
  squareFootage: number
): {
  totalCost: number;
  breakdown: Record<string, number>;
} => {
  const result = calculateProjectCost(selectedSystems, qualityTier, squareFootage);

  // Convert new breakdown format to legacy format
  const legacyBreakdown: Record<string, number> = {};
  Object.entries(result.breakdown).forEach(([systemId, systemBreakdown]) => {
    legacyBreakdown[systemId] = systemBreakdown.totalCost;
  });

  return {
    totalCost: result.totalCost,
    breakdown: legacyBreakdown
  };
};

// ===== UTILITY FUNCTIONS =====

/**
 * Get cost estimate for a single system
 */
export const getSystemCostEstimate = (
  systemId: string,
  qualityTier: string,
  squareFootage: number,
  options: Partial<CostCalculationOptions> = {}
): SystemCostBreakdown | null => {
  const systems = { [systemId]: true };
  const result = calculateProjectCost(systems, qualityTier, squareFootage, options);
  return result.breakdown[systemId] || null;
};

/**
 * Compare costs across different quality tiers
 */
export const compareTierCosts = (
  selectedSystems: Record<string, boolean> | SystemCategories,
  squareFootage: number,
  options: Partial<CostCalculationOptions> = {}
): Record<string, ProjectCostResult> => {
  const comparison: Record<string, ProjectCostResult> = {};

  Object.keys(qualityTiers).forEach(tier => {
    comparison[tier] = calculateProjectCost(selectedSystems, tier, squareFootage, options);
  });

  return comparison;
};

/**
 * Calculate cost per room based on room count
 */
export const calculateCostPerRoom = (
  totalCost: number,
  roomCount: number
): number => {
  return roomCount > 0 ? totalCost / roomCount : totalCost;
};

/**
 * Get recommended quality tier based on budget
 */
export const getRecommendedTier = (
  selectedSystems: Record<string, boolean> | SystemCategories,
  squareFootage: number,
  budget: number,
  options: Partial<CostCalculationOptions> = {}
): string => {
  const comparison = compareTierCosts(selectedSystems, squareFootage, options);

  // Find the highest tier that fits within budget
  if (comparison.premium.totalCost <= budget) return 'premium';
  if (comparison.standard.totalCost <= budget) return 'standard';
  return 'basic';
};

/**
 * Format currency for display
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

/**
 * Calculate financing options
 */
export const calculateFinancingOptions = (
  totalCost: number,
  downPaymentPercentage: number = 20,
  interestRate: number = 6.5,
  termYears: number = 5
): {
  downPayment: number;
  loanAmount: number;
  monthlyPayment: number;
  totalInterest: number;
} => {
  const downPayment = totalCost * (downPaymentPercentage / 100);
  const loanAmount = totalCost - downPayment;
  const monthlyRate = interestRate / 100 / 12;
  const numPayments = termYears * 12;

  const monthlyPayment = loanAmount *
    (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
    (Math.pow(1 + monthlyRate, numPayments) - 1);

  const totalInterest = (monthlyPayment * numPayments) - loanAmount;

  return {
    downPayment,
    loanAmount,
    monthlyPayment,
    totalInterest
  };
};

/**
 * Generate cost report summary
 */
export const generateCostReport = (
  result: ProjectCostResult,
  clientName: string = 'Client',
  projectName: string = 'Home Automation Project'
): {
  title: string;
  summary: string;
  details: string[];
  recommendations: string[];
} => {
  const { summary, breakdown } = result;
  const systemCount = Object.keys(breakdown).length;

  return {
    title: `${projectName} - Cost Estimate`,
    summary: `Total project cost: ${formatCurrency(summary.grandTotal)} for ${systemCount} systems (${formatCurrency(summary.costPerSqft)}/sq ft)`,
    details: [
      `Materials: ${formatCurrency(summary.totalMaterials)}`,
      `Labor: ${formatCurrency(summary.totalLabor)}`,
      `Markup: ${formatCurrency(summary.markup)}`,
      `Tax: ${formatCurrency(summary.tax)}`,
      `Grand Total: ${formatCurrency(summary.grandTotal)}`
    ],
    recommendations: [
      systemCount > 5 ? 'Consider phased installation to spread costs' : 'Single-phase installation recommended',
      summary.costPerSqft > 15 ? 'Premium tier selected - excellent long-term value' : 'Cost-effective solution selected',
      'Professional installation ensures optimal performance and warranty coverage'
    ]
  };
};

// ===== BACKWARD COMPATIBILITY EXPORTS =====

/**
 * Legacy square footage multipliers for backward compatibility
 * @deprecated Use qualityTiers[tier].sqftRate instead
 */
export const sqftMultipliers = {
  basic: qualityTiers.basic.sqftRate,
  standard: qualityTiers.standard.sqftRate,
  premium: qualityTiers.premium.sqftRate,
};

// ===== DEFAULT EXPORT =====

export default {
  qualityTiers,
  costMatrixCategories,
  regionalMultipliers,
  calculateProjectCost,
  calculateLegacyProjectCost,
  getSystemCostEstimate,
  compareTierCosts,
  getRecommendedTier,
  formatCurrency,
  calculateFinancingOptions,
  generateCostReport
};
