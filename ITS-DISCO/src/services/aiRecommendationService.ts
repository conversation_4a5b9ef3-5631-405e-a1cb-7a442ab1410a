// AI Recommendation Service for Reference Quote Learning System

import { supabase } from '@/integrations/supabase/client';
import { generateRecommendations, getOpenAIKey } from './openaiService';
import { 
  AIRecommendation, 
  RecommendationData, 
  ReferenceQuote,
  QuoteAIAnalysis 
} from '@/types/referenceQuoteTypes';
import { FormData } from '@/types/formTypes';
import { RoomConfig } from '@/types/roomTypes';

// Generate recommendations for current walkthrough context
export const generateWalkthroughRecommendations = async (
  walkthroughId: string,
  formData: FormData,
  stepContext: string,
  roomContext?: string
): Promise<AIRecommendation[]> => {
  try {
    // Get relevant reference quotes based on current context
    const relevantQuotes = await findRelevantQuotes(formData);
    
    if (relevantQuotes.length === 0) {
      return [];
    }
    
    // Generate different types of recommendations
    const recommendations: AIRecommendation[] = [];
    
    // System suggestions
    const systemRecommendations = await generateSystemRecommendations(
      walkthroughId,
      formData,
      stepContext,
      roomContext,
      relevantQuotes
    );
    recommendations.push(...systemRecommendations);
    
    // Product combinations
    const productRecommendations = await generateProductRecommendations(
      walkthroughId,
      formData,
      stepContext,
      roomContext,
      relevantQuotes
    );
    recommendations.push(...productRecommendations);
    
    // Cost optimizations
    const costRecommendations = await generateCostOptimizations(
      walkthroughId,
      formData,
      stepContext,
      roomContext,
      relevantQuotes
    );
    recommendations.push(...costRecommendations);
    
    // Save recommendations to database
    await saveRecommendations(recommendations);
    
    return recommendations;
    
  } catch (error) {
    console.error('Error generating walkthrough recommendations:', error);
    return [];
  }
};

// Find relevant quotes based on current walkthrough context
const findRelevantQuotes = async (formData: FormData): Promise<ReferenceQuote[]> => {
  try {
    let query = supabase
      .from('reference_quotes')
      .select('*')
      .eq('processing_status', 'completed');
    
    // Filter by project type if available
    if (formData.clientInfo.projectType) {
      query = query.eq('project_type', formData.clientInfo.projectType);
    }
    
    // Filter by budget range (±30%)
    if (formData.qualificationInfo.budget) {
      const budgetMin = formData.qualificationInfo.budget * 0.7;
      const budgetMax = formData.qualificationInfo.budget * 1.3;
      query = query.gte('estimated_budget', budgetMin).lte('estimated_budget', budgetMax);
    }
    
    // Filter by selected systems
    const selectedSystems = Object.entries(formData.systemCategories)
      .filter(([_, selected]) => selected)
      .map(([system]) => system);
    
    if (selectedSystems.length > 0) {
      query = query.overlaps('system_categories', selectedSystems);
    }
    
    // Order by confidence score and limit results
    query = query.order('confidence_score', { ascending: false }).limit(10);
    
    const { data: quotes, error } = await query;
    
    if (error) {
      throw new Error(`Failed to find relevant quotes: ${error.message}`);
    }
    
    return quotes || [];
    
  } catch (error) {
    console.error('Error finding relevant quotes:', error);
    return [];
  }
};

// Generate system recommendations
const generateSystemRecommendations = async (
  walkthroughId: string,
  formData: FormData,
  stepContext: string,
  roomContext: string | undefined,
  relevantQuotes: ReferenceQuote[]
): Promise<AIRecommendation[]> => {
  const recommendations: AIRecommendation[] = [];
  
  try {
    // Analyze patterns from relevant quotes
    const systemPatterns = analyzeSystemPatterns(relevantQuotes);
    
    // Get current room configuration if in room context
    const currentRoom = roomContext ? 
      formData.rooms.find(room => room.name === roomContext) : null;
    
    // Generate AI prompt for system recommendations
    const prompt = buildSystemRecommendationPrompt(
      formData,
      currentRoom,
      systemPatterns,
      relevantQuotes
    );
    
    const apiKey = await getOpenAIKey();
    if (!apiKey) {
      console.warn('OpenAI API key not available for recommendations');
      return recommendations;
    }
    
    const aiResponse = await generateRecommendations(
      "You are an expert home automation consultant. Analyze the provided project data and reference quotes to suggest relevant systems and configurations.",
      prompt,
      apiKey
    );
    
    if (aiResponse.success) {
      // Parse AI response and create recommendation objects
      const parsedRecommendations = parseSystemRecommendations(
        aiResponse.content,
        walkthroughId,
        stepContext,
        roomContext,
        relevantQuotes.map(q => q.id)
      );
      recommendations.push(...parsedRecommendations);
    }
    
  } catch (error) {
    console.error('Error generating system recommendations:', error);
  }
  
  return recommendations;
};

// Generate product combination recommendations
const generateProductRecommendations = async (
  walkthroughId: string,
  formData: FormData,
  stepContext: string,
  roomContext: string | undefined,
  relevantQuotes: ReferenceQuote[]
): Promise<AIRecommendation[]> => {
  const recommendations: AIRecommendation[] = [];
  
  try {
    // Analyze product patterns from quotes
    const productPatterns = analyzeProductPatterns(relevantQuotes);
    
    // Generate recommendations based on patterns
    for (const pattern of productPatterns) {
      if (pattern.confidence > 0.7) {
        const recommendation: AIRecommendation = {
          id: generateRecommendationId(),
          walkthroughId,
          recommendationType: 'product_combination',
          stepContext,
          roomContext,
          recommendationData: {
            title: `Recommended Product Combination: ${pattern.name}`,
            description: pattern.description,
            suggestedAction: `Consider adding ${pattern.products.join(', ')} together`,
            benefits: pattern.benefits,
            costImpact: {
              estimated: pattern.averageCost,
              range: { min: pattern.costRange.min, max: pattern.costRange.max },
              confidence: pattern.confidence
            }
          },
          confidenceScore: pattern.confidence,
          sourceQuotes: pattern.sourceQuotes,
          reasoning: pattern.reasoning,
          status: 'pending',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        recommendations.push(recommendation);
      }
    }
    
  } catch (error) {
    console.error('Error generating product recommendations:', error);
  }
  
  return recommendations;
};

// Generate cost optimization recommendations
const generateCostOptimizations = async (
  walkthroughId: string,
  formData: FormData,
  stepContext: string,
  roomContext: string | undefined,
  relevantQuotes: ReferenceQuote[]
): Promise<AIRecommendation[]> => {
  const recommendations: AIRecommendation[] = [];
  
  try {
    // Analyze cost patterns and identify optimization opportunities
    const costAnalysis = analyzeCostPatterns(relevantQuotes, formData);
    
    for (const optimization of costAnalysis.optimizations) {
      if (optimization.potentialSavings > 1000) { // Only suggest significant savings
        const recommendation: AIRecommendation = {
          id: generateRecommendationId(),
          walkthroughId,
          recommendationType: 'cost_optimization',
          stepContext,
          roomContext,
          recommendationData: {
            title: `Cost Optimization: ${optimization.title}`,
            description: optimization.description,
            suggestedAction: optimization.action,
            costImpact: {
              estimated: -optimization.potentialSavings, // Negative for savings
              range: { 
                min: -optimization.savingsRange.max, 
                max: -optimization.savingsRange.min 
              },
              confidence: optimization.confidence
            },
            benefits: optimization.benefits,
            considerations: optimization.considerations
          },
          confidenceScore: optimization.confidence,
          sourceQuotes: optimization.sourceQuotes,
          reasoning: optimization.reasoning,
          status: 'pending',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        recommendations.push(recommendation);
      }
    }
    
  } catch (error) {
    console.error('Error generating cost optimizations:', error);
  }
  
  return recommendations;
};

// Analyze system patterns from reference quotes
const analyzeSystemPatterns = (quotes: ReferenceQuote[]): any[] => {
  const patterns: any[] = [];
  
  // Group quotes by system combinations
  const systemCombinations = new Map<string, any>();
  
  quotes.forEach(quote => {
    if (quote.aiAnalysis?.projectPatterns?.commonSystemCombinations) {
      quote.aiAnalysis.projectPatterns.commonSystemCombinations.forEach(combo => {
        const key = combo.systems.sort().join(',');
        if (!systemCombinations.has(key)) {
          systemCombinations.set(key, {
            systems: combo.systems,
            frequency: 0,
            totalCost: 0,
            quotes: []
          });
        }
        
        const pattern = systemCombinations.get(key)!;
        pattern.frequency += combo.frequency;
        pattern.totalCost += combo.averageCost;
        pattern.quotes.push(quote.id);
      });
    }
  });
  
  // Convert to array and calculate averages
  systemCombinations.forEach((pattern, key) => {
    patterns.push({
      systems: pattern.systems,
      frequency: pattern.frequency,
      averageCost: pattern.totalCost / pattern.quotes.length,
      confidence: Math.min(pattern.frequency / quotes.length, 1),
      sourceQuotes: pattern.quotes
    });
  });
  
  return patterns.sort((a, b) => b.confidence - a.confidence);
};

// Analyze product patterns from reference quotes
const analyzeProductPatterns = (quotes: ReferenceQuote[]): any[] => {
  // Implementation for product pattern analysis
  return [];
};

// Analyze cost patterns and identify optimizations
const analyzeCostPatterns = (quotes: ReferenceQuote[], formData: FormData): any => {
  // Implementation for cost pattern analysis
  return { optimizations: [] };
};

// Build system recommendation prompt for AI
const buildSystemRecommendationPrompt = (
  formData: FormData,
  currentRoom: RoomConfig | null,
  systemPatterns: any[],
  relevantQuotes: ReferenceQuote[]
): string => {
  const selectedSystems = Object.entries(formData.systemCategories)
    .filter(([_, selected]) => selected)
    .map(([system]) => system);
  
  return `
    Analyze this client walkthrough and provide system recommendations:
    
    Client Info:
    - Project Type: ${formData.clientInfo.projectType}
    - Budget: $${formData.qualificationInfo.budget?.toLocaleString()}
    - Square Footage: ${formData.qualificationInfo.squareFootage}
    - Selected Systems: ${selectedSystems.join(', ')}
    
    ${currentRoom ? `Current Room: ${currentRoom.name} (${currentRoom.type})` : ''}
    
    Based on ${relevantQuotes.length} similar projects, provide specific recommendations for:
    1. Additional systems that would complement current selections
    2. System configurations that work well together
    3. Potential upgrades or alternatives
    
    Focus on practical, cost-effective suggestions with clear benefits.
  `;
};

// Parse AI response into recommendation objects
const parseSystemRecommendations = (
  aiContent: string,
  walkthroughId: string,
  stepContext: string,
  roomContext: string | undefined,
  sourceQuotes: string[]
): AIRecommendation[] => {
  // Simple parsing - in production, use more sophisticated NLP
  const recommendations: AIRecommendation[] = [];
  
  // This is a simplified implementation
  // In production, you'd parse the AI response more intelligently
  
  return recommendations;
};

// Save recommendations to database
const saveRecommendations = async (recommendations: AIRecommendation[]): Promise<void> => {
  try {
    if (recommendations.length === 0) return;
    
    const { error } = await supabase
      .from('ai_recommendations')
      .insert(recommendations.map(rec => ({
        walkthrough_id: rec.walkthroughId,
        recommendation_type: rec.recommendationType,
        step_context: rec.stepContext,
        room_context: rec.roomContext,
        recommendation_data: rec.recommendationData,
        confidence_score: rec.confidenceScore,
        source_quotes: rec.sourceQuotes,
        reasoning: rec.reasoning,
        status: rec.status
      })));
    
    if (error) {
      throw new Error(`Failed to save recommendations: ${error.message}`);
    }
    
  } catch (error) {
    console.error('Error saving recommendations:', error);
  }
};

// Get recommendations for a walkthrough
export const getWalkthroughRecommendations = async (
  walkthroughId: string,
  stepContext?: string,
  roomContext?: string
): Promise<AIRecommendation[]> => {
  try {
    let query = supabase
      .from('ai_recommendations')
      .select('*')
      .eq('walkthrough_id', walkthroughId)
      .eq('status', 'pending');
    
    if (stepContext) {
      query = query.eq('step_context', stepContext);
    }
    
    if (roomContext) {
      query = query.eq('room_context', roomContext);
    }
    
    query = query.order('confidence_score', { ascending: false });
    
    const { data: recommendations, error } = await query;
    
    if (error) {
      throw new Error(`Failed to get recommendations: ${error.message}`);
    }
    
    return recommendations || [];
    
  } catch (error) {
    console.error('Error getting walkthrough recommendations:', error);
    return [];
  }
};

// Update recommendation status
export const updateRecommendationStatus = async (
  recommendationId: string,
  status: 'accepted' | 'dismissed' | 'modified',
  userFeedback?: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('ai_recommendations')
      .update({
        status,
        user_feedback: userFeedback,
        updated_at: new Date().toISOString()
      })
      .eq('id', recommendationId);
    
    if (error) {
      throw new Error(`Failed to update recommendation: ${error.message}`);
    }
    
    return true;
    
  } catch (error) {
    console.error('Error updating recommendation status:', error);
    return false;
  }
};

// Utility function to generate recommendation IDs
const generateRecommendationId = (): string => {
  return `rec_${Date.now()}_${Math.random().toString(36).substring(7)}`;
};
