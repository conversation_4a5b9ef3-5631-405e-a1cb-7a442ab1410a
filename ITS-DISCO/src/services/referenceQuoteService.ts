// Reference Quote Learning System Service

import { supabase } from '@/integrations/supabase/client';
import { 
  ReferenceQuote, 
  QuoteUploadRequest, 
  QuoteSearchFilters, 
  QuoteSearchResult,
  QuoteExtractedData,
  QuoteAIAnalysis,
  QuoteProcessingJob
} from '@/types/referenceQuoteTypes';

// Debug function to test Supabase connection
export const debugSupabaseConnection = async () => {
  try {
    console.log('🔍 Testing Supabase connection...');

    // Test 1: Basic connection
    const { data: testData, error: testError } = await supabase
      .from('reference_quotes')
      .select('count', { count: 'exact', head: true });

    console.log('📊 Basic query test:', { testData, testError });

    // Test 2: Storage connection
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets();
    console.log('🗄️ Storage test:', { buckets, storageError });

    // Test 3: Auth status
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('👤 Auth test:', { user, authError });

    return {
      tableAccess: !testError,
      storageAccess: !storageError,
      authStatus: !authError,
      errors: {
        table: testError?.message,
        storage: storageError?.message,
        auth: authError?.message
      }
    };
  } catch (error) {
    console.error('❌ Debug test failed:', error);
    return { error };
  }
};

// Check if the reference quote system is properly set up
export const checkReferenceQuoteSetup = async (): Promise<{
  tablesExist: boolean;
  storageExists: boolean;
  error?: string;
}> => {
  try {
    console.log('🔍 Checking reference quote setup status...');

    // Check if reference_quotes table exists by trying a simple query
    const { data: tableData, error: tableError } = await supabase
      .from('reference_quotes')
      .select('id')
      .limit(1);

    console.log('📊 Table check result:', { tableData, tableError });

    const tablesExist = !tableError || !tableError.message.includes('does not exist');

    // Check if storage bucket exists
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets();
    console.log('🗄️ Storage check result:', { buckets, storageError });

    const storageExists = !storageError && buckets?.some(bucket => bucket.name === 'reference-quotes');

    console.log('🔍 Setup status:', { tablesExist, storageExists });

    if (tablesExist && storageExists) {
      console.log('✅ Setup check passed - tables and storage exist');
      return { tablesExist: true, storageExists: true };
    } else {
      console.log('⚠️ Setup incomplete:', {
        tablesExist,
        storageExists,
        tableError: tableError?.message,
        storageError: storageError?.message
      });

      let errorMessage = 'Database setup incomplete. ';
      if (!tablesExist) errorMessage += 'Tables missing. ';
      if (!storageExists) errorMessage += 'Storage bucket missing. ';
      errorMessage += 'Please run the setup script.';

      return {
        tablesExist,
        storageExists,
        error: errorMessage
      };
    }

  } catch (error) {
    console.error('❌ Error checking setup:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      error
    });

    return {
      tablesExist: false,
      storageExists: false,
      error: error instanceof Error ? error.message : 'Unknown setup check error'
    };
  }
};

// Transform database record to ReferenceQuote interface
const transformQuoteFromDB = (dbRecord: any): ReferenceQuote => {
  return {
    id: dbRecord.id,
    title: dbRecord.title,
    fileName: dbRecord.file_name,
    filePath: dbRecord.file_path,
    fileType: dbRecord.file_type,
    fileSize: dbRecord.file_size,
    uploadDate: dbRecord.created_at,
    processedDate: dbRecord.processed_date,
    processingStatus: dbRecord.processing_status,
    extractedData: dbRecord.extracted_data,
    aiAnalysis: dbRecord.ai_analysis,
    projectType: dbRecord.project_type,
    estimatedBudget: dbRecord.estimated_budget,
    systemCategories: dbRecord.system_categories,
    roomCount: dbRecord.room_count,
    squareFootage: dbRecord.square_footage,
    complexityScore: dbRecord.complexity_score,
    confidenceScore: dbRecord.confidence_score,
    tags: dbRecord.tags,
    teamId: dbRecord.team_id,
    uploadedBy: dbRecord.uploaded_by,
    createdAt: dbRecord.created_at,
    updatedAt: dbRecord.updated_at
  };
};

// File Upload and Storage
export const uploadReferenceQuote = async (
  uploadRequest: QuoteUploadRequest
): Promise<{ success: boolean; quoteId?: string; error?: string }> => {
  try {
    const { file, title, projectType, tags, notes } = uploadRequest;
    
    // Generate unique file path
    const fileExtension = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExtension}`;
    const filePath = `reference-quotes/${fileName}`;
    
    // Upload file to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('reference-quotes')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      // Check if bucket doesn't exist
      if (uploadError.message.includes('Bucket not found')) {
        throw new Error(`Storage bucket not found. Please run the database setup script to create the 'reference-quotes' bucket.`);
      }
      throw new Error(`File upload failed: ${uploadError.message}`);
    }
    
    // Get current user (optional for now since auth isn't implemented)
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    const userId = user?.id || '00000000-0000-0000-0000-000000000000'; // Anonymous UUID
    
    // Create database record
    const quoteData = {
      title,
      file_name: file.name,
      file_path: filePath,
      file_type: file.type,
      file_size: file.size,
      project_type: projectType,
      tags: tags || [],
      uploaded_by: userId,
      processing_status: 'pending' as const
    };
    
    const { data: quoteRecord, error: dbError } = await supabase
      .from('reference_quotes')
      .insert(quoteData)
      .select()
      .single();
    
    if (dbError) {
      // Clean up uploaded file if database insert fails
      await supabase.storage.from('reference-quotes').remove([filePath]);

      console.error('Database error details:', {
        message: dbError.message,
        details: dbError.details,
        hint: dbError.hint,
        code: dbError.code
      });

      // Check if it's a table not found error
      if (dbError.message.includes('relation "public.reference_quotes" does not exist')) {
        throw new Error('Reference quotes table not found. Please run the database setup script from the Settings page.');
      }

      throw new Error(`Database error: ${dbError.message}`);
    }
    
    // Queue for processing
    await queueQuoteProcessing(quoteRecord.id);
    
    return { success: true, quoteId: quoteRecord.id };
  } catch (error) {
    console.error('Error uploading reference quote:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
};

// Queue quote for AI processing
export const queueQuoteProcessing = async (quoteId: string): Promise<void> => {
  try {
    console.log('Queueing quote for processing:', quoteId);

    // Update status to processing
    await supabase
      .from('reference_quotes')
      .update({ processing_status: 'processing' })
      .eq('id', quoteId);

    console.log('Status updated to processing, starting AI analysis...');

    // Add a small delay to simulate processing time and ensure UI sees the status change
    setTimeout(async () => {
      await processQuoteWithAI(quoteId);
    }, 2000); // 2 second delay

  } catch (error) {
    console.error('Error queueing quote processing:', error);
    await supabase
      .from('reference_quotes')
      .update({ processing_status: 'failed' })
      .eq('id', quoteId);
  }
};

// Process quote with AI analysis
export const processQuoteWithAI = async (quoteId: string): Promise<void> => {
  console.log('Starting AI processing for quote:', quoteId);

  try {
    // Get quote record
    const { data: quote, error: quoteError } = await supabase
      .from('reference_quotes')
      .select('*')
      .eq('id', quoteId)
      .single();

    if (quoteError || !quote) {
      console.error('Quote not found:', quoteError);
      throw new Error('Quote not found');
    }

    console.log('Quote found, processing file:', quote.file_name);

    // For now, skip file download and use mock data to ensure processing completes
    // This will be replaced with real file processing later

    // Generate mock extracted data
    const extractedData = {
      rawText: `Mock extracted text from ${quote.file_name}`,
      projectDetails: {
        totalCost: 45000 + Math.random() * 50000, // Random cost between 45k-95k
        rooms: [
          { name: 'Living Room', type: 'living', systems: ['lighting', 'audio'] },
          { name: 'Kitchen', type: 'kitchen', systems: ['lighting'] }
        ],
        systems: [
          { category: 'Lighting Control', subcategory: 'Smart Switches' },
          { category: 'Audio/Video', subcategory: 'Multi-Room Audio' }
        ],
        squareFootage: 2500 + Math.random() * 2000 // Random between 2500-4500 sq ft
      },
      metadata: {
        extractionMethod: 'mock_processing',
        extractionDate: new Date().toISOString(),
        confidence: 0.85
      }
    };

    // Generate AI analysis
    const aiAnalysis = await generateAIAnalysis(extractedData, quote);

    console.log('Generated AI analysis, updating database...');

    // Update quote record with results
    const { error: updateError } = await supabase
      .from('reference_quotes')
      .update({
        processing_status: 'completed',
        processed_date: new Date().toISOString(),
        extracted_data: extractedData,
        ai_analysis: aiAnalysis,
        estimated_budget: extractedData.projectDetails?.totalCost,
        system_categories: extractedData.projectDetails?.systems?.map(s => s.category),
        room_count: extractedData.projectDetails?.rooms?.length,
        square_footage: extractedData.projectDetails?.squareFootage,
        complexity_score: aiAnalysis.confidence.overallScore,
        confidence_score: aiAnalysis.confidence.overallScore
      })
      .eq('id', quoteId);

    if (updateError) {
      console.error('Error updating quote:', updateError);
      console.error('Update error details:', {
        message: updateError.message,
        details: updateError.details,
        hint: updateError.hint,
        code: updateError.code
      });

      // Check if it's a table not found error
      if (updateError.message.includes('relation "public.reference_quotes" does not exist')) {
        throw new Error('Reference quotes table not found. Please run the database setup script.');
      }

      throw updateError;
    }

    console.log('Quote processing completed successfully:', quoteId);

  } catch (error) {
    console.error('Error processing quote with AI:', error);
    await supabase
      .from('reference_quotes')
      .update({
        processing_status: 'failed',
        processed_date: new Date().toISOString()
      })
      .eq('id', quoteId);
  }
};

// Extract data from uploaded file
const extractDataFromFile = async (
  fileData: Blob, 
  fileType: string
): Promise<QuoteExtractedData> => {
  // This is a simplified implementation
  // In production, you'd use specialized libraries for each file type
  
  const extractedData: QuoteExtractedData = {
    metadata: {
      extractionMethod: 'pdf_text',
      extractionDate: new Date().toISOString(),
      confidence: 0.8
    }
  };
  
  try {
    if (fileType.includes('pdf')) {
      // Use PDF.js or similar library
      extractedData.rawText = await extractTextFromPDF(fileData);
    } else if (fileType.includes('csv')) {
      // Parse CSV data
      extractedData.lineItems = await parseCSVData(fileData);
    } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
      // Parse Excel data
      extractedData.lineItems = await parseExcelData(fileData);
    } else if (fileType.includes('image')) {
      // Use OCR service
      extractedData.rawText = await performOCR(fileData);
      extractedData.metadata.extractionMethod = 'ocr';
    } else {
      // Try to extract as text
      extractedData.rawText = await fileData.text();
    }
    
    // Parse structured data from raw text if available
    if (extractedData.rawText) {
      extractedData.projectDetails = await parseProjectDetails(extractedData.rawText);
    }
    
  } catch (error) {
    console.error('Error extracting data from file:', error);
    extractedData.metadata.confidence = 0.3;
  }
  
  return extractedData;
};

// Placeholder functions for file processing
const extractTextFromPDF = async (fileData: Blob): Promise<string> => {
  // Implement PDF text extraction
  return "Sample PDF text content";
};

const parseCSVData = async (fileData: Blob): Promise<any[]> => {
  // Implement CSV parsing
  return [];
};

const parseExcelData = async (fileData: Blob): Promise<any[]> => {
  // Implement Excel parsing
  return [];
};

const performOCR = async (fileData: Blob): Promise<string> => {
  // Implement OCR processing
  return "Sample OCR text content";
};

const parseProjectDetails = async (text: string): Promise<any> => {
  // Implement intelligent text parsing
  return {
    totalCost: 50000,
    rooms: [],
    systems: []
  };
};

// Generate AI analysis of extracted data
const generateAIAnalysis = async (
  extractedData: QuoteExtractedData,
  quote: any
): Promise<QuoteAIAnalysis> => {
  // This would integrate with OpenAI or similar service
  // For now, return a mock analysis
  
  return {
    projectPatterns: {
      commonSystemCombinations: [],
      typicalRoomConfigurations: [],
      costPatterns: [],
      complexityIndicators: []
    },
    recommendations: {
      suggestedSystems: [],
      potentialUpsells: [],
      costOptimizations: [],
      riskFactors: []
    },
    benchmarks: {
      costPerSquareFoot: 25,
      laborToMaterialRatio: 0.4,
      systemDistribution: {},
      timelineEstimate: "8-12 weeks"
    },
    confidence: {
      overallScore: 0.85,
      dataQuality: 0.8,
      extractionAccuracy: 0.9,
      analysisReliability: 0.8
    },
    processingMetadata: {
      analysisDate: new Date().toISOString(),
      modelVersion: "1.0.0",
      processingTime: 5000
    }
  };
};

// Search and retrieve quotes
export const searchReferenceQuotes = async (
  filters: QuoteSearchFilters = {},
  page: number = 1,
  pageSize: number = 20
): Promise<QuoteSearchResult> => {
  try {
    let query = supabase
      .from('reference_quotes')
      .select('*', { count: 'exact' });

    // Apply filters
    if (filters.projectType) {
      query = query.eq('project_type', filters.projectType);
    }

    if (filters.systemCategories?.length) {
      query = query.overlaps('system_categories', filters.systemCategories);
    }

    if (filters.processingStatus?.length) {
      query = query.in('processing_status', filters.processingStatus);
    }

    if (filters.searchText) {
      query = query.or(`title.ilike.%${filters.searchText}%,file_name.ilike.%${filters.searchText}%`);
    }

    // Apply pagination
    const offset = (page - 1) * pageSize;
    query = query.range(offset, offset + pageSize - 1);

    // Order by creation date
    query = query.order('created_at', { ascending: false });

    const { data: quotes, error, count } = await query;

    if (error) {
      // Check if the table doesn't exist yet
      if (error.message.includes('relation "public.reference_quotes" does not exist')) {
        console.warn('Reference quotes table not found. Please run the database setup script.');
        return {
          quotes: [],
          totalCount: 0,
          facets: {
            projectTypes: [],
            systemCategories: [],
            tags: []
          }
        };
      }
      throw new Error(`Search failed: ${error.message}`);
    }

    return {
      quotes: quotes?.map(transformQuoteFromDB) || [],
      totalCount: count || 0,
      facets: {
        projectTypes: [],
        systemCategories: [],
        tags: []
      }
    };

  } catch (error) {
    console.error('Error searching reference quotes:', error);
    // Always throw the error so the UI can detect setup issues
    throw error;
  }
};

// Get quote by ID
export const getReferenceQuote = async (id: string): Promise<ReferenceQuote | null> => {
  try {
    const { data: quote, error } = await supabase
      .from('reference_quotes')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      throw new Error(`Failed to get quote: ${error.message}`);
    }

    return quote ? transformQuoteFromDB(quote) : null;
  } catch (error) {
    console.error('Error getting reference quote:', error);
    return null;
  }
};

// Delete quote
export const deleteReferenceQuote = async (id: string): Promise<boolean> => {
  try {
    // Get quote to find file path
    const quote = await getReferenceQuote(id);
    if (!quote) {
      throw new Error('Quote not found');
    }

    // Delete file from storage
    await supabase.storage
      .from('reference-quotes')
      .remove([quote.filePath]);

    // Delete database record
    const { error } = await supabase
      .from('reference_quotes')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete quote: ${error.message}`);
    }

    return true;
  } catch (error) {
    console.error('Error deleting reference quote:', error);
    return false;
  }
};

// ===== REPROCESSING FUNCTIONS =====

// Reprocess a single quote
export const reprocessQuote = async (quoteId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log('🔄 Starting reprocessing for quote:', quoteId);

    // Reset quote status to pending
    const { error: resetError } = await supabase
      .from('reference_quotes')
      .update({
        processing_status: 'pending',
        processed_date: null,
        extracted_data: null,
        ai_analysis: null
      })
      .eq('id', quoteId);

    if (resetError) {
      console.error('Error resetting quote status:', resetError);
      return { success: false, error: 'Failed to reset quote status' };
    }

    // Queue for processing
    await queueQuoteProcessing(quoteId);

    console.log('✅ Quote queued for reprocessing:', quoteId);
    return { success: true };

  } catch (error) {
    console.error('Error reprocessing quote:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Bulk reprocess multiple quotes
export const bulkReprocessQuotes = async (
  quoteIds: string[]
): Promise<{
  success: boolean;
  processed: number;
  failed: string[];
  errors: Record<string, string>
}> => {
  console.log('🔄 Starting bulk reprocessing for quotes:', quoteIds);

  const failed: string[] = [];
  const errors: Record<string, string> = {};
  let processed = 0;

  for (const quoteId of quoteIds) {
    try {
      const result = await reprocessQuote(quoteId);
      if (result.success) {
        processed++;
      } else {
        failed.push(quoteId);
        errors[quoteId] = result.error || 'Unknown error';
      }

      // Add small delay between requests to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      failed.push(quoteId);
      errors[quoteId] = error instanceof Error ? error.message : 'Unknown error';
    }
  }

  console.log(`✅ Bulk reprocessing completed: ${processed} processed, ${failed.length} failed`);

  return {
    success: failed.length === 0,
    processed,
    failed,
    errors
  };
};

// Get quotes that are stuck in processing states
export const getStuckQuotes = async (): Promise<ReferenceQuote[]> => {
  try {
    // Get quotes that have been in 'pending' or 'processing' state for more than 10 minutes
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();

    const { data: quotes, error } = await supabase
      .from('reference_quotes')
      .select('*')
      .in('processing_status', ['pending', 'processing'])
      .lt('created_at', tenMinutesAgo)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching stuck quotes:', error);
      return [];
    }

    return quotes?.map(transformQuoteFromDB) || [];

  } catch (error) {
    console.error('Error getting stuck quotes:', error);
    return [];
  }
};

// Reprocess all stuck quotes
export const reprocessAllStuckQuotes = async (): Promise<{
  success: boolean;
  totalFound: number;
  processed: number;
  failed: string[];
  errors: Record<string, string>;
}> => {
  try {
    console.log('🔍 Finding stuck quotes...');
    const stuckQuotes = await getStuckQuotes();

    if (stuckQuotes.length === 0) {
      console.log('✅ No stuck quotes found');
      return {
        success: true,
        totalFound: 0,
        processed: 0,
        failed: [],
        errors: {}
      };
    }

    console.log(`📋 Found ${stuckQuotes.length} stuck quotes, starting reprocessing...`);

    const quoteIds = stuckQuotes.map(q => q.id);
    const result = await bulkReprocessQuotes(quoteIds);

    return {
      success: result.success,
      totalFound: stuckQuotes.length,
      processed: result.processed,
      failed: result.failed,
      errors: result.errors
    };

  } catch (error) {
    console.error('Error reprocessing stuck quotes:', error);
    return {
      success: false,
      totalFound: 0,
      processed: 0,
      failed: [],
      errors: { general: error instanceof Error ? error.message : 'Unknown error' }
    };
  }
};
