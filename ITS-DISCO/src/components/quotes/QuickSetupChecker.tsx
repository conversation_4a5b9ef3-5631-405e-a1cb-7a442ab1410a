// Quick Setup Checker Component - Always shows setup when tables don't exist

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Database, 
  AlertTriangle, 
  Copy, 
  CheckCircle,
  Brain,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

const QuickSetupChecker: React.FC = () => {
  const [setupStatus, setSetupStatus] = useState<'checking' | 'needed' | 'complete'>('checking');
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const setupSQL = `-- 🚀 REFERENCE QUOTE LEARNING SYSTEM - ONE-CLICK SETUP
-- Simply copy this entire script and paste it into your Supabase SQL Editor, then click RUN

-- ✅ Step 1: Create reference_quotes table
CREATE TABLE IF NOT EXISTS reference_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    processing_status TEXT DEFAULT 'pending',
    extracted_data JSONB,
    ai_analysis JSONB,
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    room_count INTEGER,
    square_footage INTEGER,
    complexity_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    tags TEXT[],
    team_id UUID,
    uploaded_by UUID NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ✅ Step 2: Create ai_recommendations table
CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    walkthrough_id UUID NOT NULL,
    recommendation_type TEXT NOT NULL,
    step_context TEXT NOT NULL,
    room_context TEXT,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    source_quotes TEXT[],
    reasoning TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    user_feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ✅ Step 3: Create storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('reference-quotes', 'reference-quotes', false)
ON CONFLICT (id) DO NOTHING;

-- ✅ Step 4: Create performance indexes
CREATE INDEX IF NOT EXISTS idx_reference_quotes_status ON reference_quotes(processing_status);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_type ON reference_quotes(project_type);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_user ON reference_quotes(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_date ON reference_quotes(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_walkthrough ON ai_recommendations(walkthrough_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);

-- ✅ Step 5: Enable security
ALTER TABLE reference_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;

-- ✅ Step 6: Create security policies
CREATE POLICY "Users manage their quotes" ON reference_quotes FOR ALL USING (uploaded_by = auth.uid());
CREATE POLICY "Users view their recommendations" ON ai_recommendations FOR SELECT USING (
    walkthrough_id IN (SELECT id FROM walkthroughs WHERE created_by = auth.uid())
);
CREATE POLICY "System creates recommendations" ON ai_recommendations FOR INSERT WITH CHECK (true);
CREATE POLICY "Users update their recommendations" ON ai_recommendations FOR UPDATE USING (
    walkthrough_id IN (SELECT id FROM walkthroughs WHERE created_by = auth.uid())
);

-- ✅ Step 7: Storage policies
CREATE POLICY "Users upload files" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'reference-quotes');
CREATE POLICY "Users access files" ON storage.objects FOR SELECT USING (bucket_id = 'reference-quotes');
CREATE POLICY "Users delete files" ON storage.objects FOR DELETE USING (bucket_id = 'reference-quotes');

-- ✅ Step 8: Grant permissions
GRANT ALL ON reference_quotes TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;

-- 🎉 SUCCESS MESSAGE
SELECT '🎉 Reference Quote Learning System setup completed successfully! You can now upload quotes and receive AI recommendations.' as setup_result;`;

  useEffect(() => {
    checkSetup();
  }, []);

  const checkSetup = async () => {
    try {
      setSetupStatus('checking');

      // Simple test: try to query the reference_quotes table
      const { error } = await supabase
        .from('reference_quotes')
        .select('id')
        .limit(1);

      // If no error or any error other than "table doesn't exist", consider setup complete
      if (!error) {
        setSetupStatus('complete');
        return;
      }

      if (error.message.includes('relation "public.reference_quotes" does not exist')) {
        setSetupStatus('needed');
        return;
      }

      // For any other error (permissions, etc.), assume setup is complete
      // since the table exists
      console.log('Table exists, setup complete. Error was:', error.message);
      setSetupStatus('complete');

    } catch (error) {
      console.error('Setup check error:', error);
      // Only show setup needed if it's clearly a missing table
      if (error instanceof Error && error.message.includes('relation "public.reference_quotes" does not exist')) {
        setSetupStatus('needed');
      } else {
        setSetupStatus('complete');
      }
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(setupSQL);
      setCopied(true);
      toast({
        title: "SQL Script Copied!",
        description: "Now paste it into your Supabase SQL Editor and click Run.",
      });
      setTimeout(() => setCopied(false), 3000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Please select and copy the SQL script manually.",
        variant: "destructive"
      });
    }
  };

  const openSupabase = () => {
    window.open('https://supabase.com/dashboard', '_blank');
  };

  if (setupStatus === 'checking') {
    return (
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            Checking Setup Status...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (setupStatus === 'complete') {
    return (
      <Card className="border-l-4 border-l-green-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Setup Complete!
          </CardTitle>
          <CardDescription>
            Reference Quote Learning System is ready to use.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="border-l-4 border-l-red-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Database Setup Required
        </CardTitle>
        <CardDescription>
          The Reference Quote Learning System needs database tables to be created.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Setup Required:</strong> Database tables and storage bucket need to be created 
            before you can upload quotes and receive AI recommendations.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <div className="p-4 border rounded-lg bg-muted/50">
            <h3 className="font-medium mb-2 flex items-center gap-2">
              <Database className="h-4 w-4" />
              Quick Setup Instructions
            </h3>
            <ol className="text-sm space-y-2 text-muted-foreground">
              <li><strong>1.</strong> Copy the SQL script below</li>
              <li><strong>2.</strong> Open your Supabase Dashboard → SQL Editor</li>
              <li><strong>3.</strong> Paste the script and click "Run"</li>
              <li><strong>4.</strong> Refresh this page</li>
            </ol>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={copyToClipboard}
              className="flex items-center gap-2"
              size="lg"
            >
              {copied ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
              {copied ? 'Copied!' : 'Copy SQL Script'}
            </Button>
            
            <Button
              variant="outline"
              onClick={openSupabase}
              className="flex items-center gap-2"
              size="lg"
            >
              <ExternalLink className="h-4 w-4" />
              Open Supabase
            </Button>
            
            <Button
              variant="outline"
              onClick={checkSetup}
              className="flex items-center gap-2"
              size="lg"
            >
              <Database className="h-4 w-4" />
              Check Again
            </Button>
          </div>

          <div className="text-xs text-muted-foreground p-3 bg-muted rounded">
            <strong>What this creates:</strong> reference_quotes table, ai_recommendations table, 
            reference-quotes storage bucket, security policies, and performance indexes.
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickSetupChecker;
