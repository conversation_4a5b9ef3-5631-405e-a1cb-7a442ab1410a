// Setup Notification Component for Reference Quote Learning System

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  AlertTriangle, 
  CheckCircle, 
  Copy, 
  ExternalLink,
  Brain,
  Settings,
  FileText
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SetupNotificationProps {
  onSetupComplete?: () => void;
}

const SetupNotification: React.FC<SetupNotificationProps> = ({ onSetupComplete }) => {
  const [copiedStep, setCopiedStep] = useState<number | null>(null);
  const { toast } = useToast();

  const setupSteps = [
    {
      id: 1,
      title: "Database Setup",
      description: "Create the necessary database tables and storage bucket",
      status: "required",
      action: "Copy SQL Script",
      sqlScript: `-- Reference Quote Learning System Database Setup
-- Copy and paste this entire script into your Supabase SQL Editor and run it

-- 1. Create reference_quotes table
CREATE TABLE IF NOT EXISTS reference_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    extracted_data JSONB,
    ai_analysis JSONB,
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    room_count INTEGER,
    square_footage INTEGER,
    complexity_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    tags TEXT[],
    team_id UUID,
    uploaded_by UUID NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Create ai_recommendations table
CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    walkthrough_id UUID NOT NULL,
    recommendation_type TEXT NOT NULL CHECK (recommendation_type IN ('system_suggestion', 'product_combination', 'cost_optimization', 'configuration_option')),
    step_context TEXT NOT NULL,
    room_context TEXT,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    source_quotes TEXT[],
    reasoning TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'dismissed', 'modified')),
    user_feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create storage bucket for reference quotes
INSERT INTO storage.buckets (id, name, public) 
VALUES ('reference-quotes', 'reference-quotes', false)
ON CONFLICT (id) DO NOTHING;

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reference_quotes_processing_status ON reference_quotes(processing_status);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_project_type ON reference_quotes(project_type);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_system_categories ON reference_quotes USING GIN(system_categories);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_tags ON reference_quotes USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_team_id ON reference_quotes(team_id);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_uploaded_by ON reference_quotes(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_created_at ON reference_quotes(created_at);

CREATE INDEX IF NOT EXISTS idx_ai_recommendations_walkthrough_id ON ai_recommendations(walkthrough_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_step_context ON ai_recommendations(step_context);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_confidence ON ai_recommendations(confidence_score);

-- 5. Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. Create triggers for updated_at
CREATE TRIGGER update_reference_quotes_updated_at 
    BEFORE UPDATE ON reference_quotes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_recommendations_updated_at 
    BEFORE UPDATE ON ai_recommendations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 7. Enable Row Level Security
ALTER TABLE reference_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies for reference_quotes
CREATE POLICY "Users can view their own reference quotes" ON reference_quotes
    FOR SELECT USING (uploaded_by = auth.uid());

CREATE POLICY "Users can insert their own reference quotes" ON reference_quotes
    FOR INSERT WITH CHECK (uploaded_by = auth.uid());

CREATE POLICY "Users can update their own reference quotes" ON reference_quotes
    FOR UPDATE USING (uploaded_by = auth.uid());

CREATE POLICY "Users can delete their own reference quotes" ON reference_quotes
    FOR DELETE USING (uploaded_by = auth.uid());

-- 9. Create RLS policies for ai_recommendations
CREATE POLICY "Users can view recommendations for their walkthroughs" ON ai_recommendations
    FOR SELECT USING (
        walkthrough_id IN (
            SELECT id FROM walkthroughs WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "System can insert recommendations" ON ai_recommendations
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update recommendations for their walkthroughs" ON ai_recommendations
    FOR UPDATE USING (
        walkthrough_id IN (
            SELECT id FROM walkthroughs WHERE created_by = auth.uid()
        )
    );

-- 10. Create storage policies for reference-quotes bucket
CREATE POLICY "Users can upload reference quote files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'reference-quotes'
    );

CREATE POLICY "Users can view reference quote files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'reference-quotes'
    );

CREATE POLICY "Users can delete reference quote files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'reference-quotes'
    );

-- 11. Grant necessary permissions
GRANT ALL ON reference_quotes TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;

-- Success message
SELECT 'Reference Quote Learning System setup completed successfully!' as message;`
    },
    {
      id: 2,
      title: "OpenAI Configuration",
      description: "Configure OpenAI API key for AI analysis and recommendations",
      status: "recommended",
      action: "Go to Settings"
    },
    {
      id: 3,
      title: "Upload Test Quote",
      description: "Upload your first reference quote to test the system",
      status: "optional",
      action: "Upload Quote"
    }
  ];

  const copyToClipboard = async (text: string, stepId: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStep(stepId);
      toast({
        title: "Copied to Clipboard",
        description: "SQL script copied successfully. Paste it into your Supabase SQL Editor.",
      });
      setTimeout(() => setCopiedStep(null), 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard. Please select and copy manually.",
        variant: "destructive"
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'required':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'recommended':
        return <Settings className="h-4 w-4 text-yellow-500" />;
      case 'optional':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'required':
        return <Badge variant="destructive">Required</Badge>;
      case 'recommended':
        return <Badge variant="secondary">Recommended</Badge>;
      case 'optional':
        return <Badge variant="outline">Optional</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Reference Quote Learning System Setup
        </CardTitle>
        <CardDescription>
          Complete these steps to enable AI-powered recommendations and quote analysis.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            The Reference Quote Learning System requires database setup before use. 
            Please complete the steps below to get started.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          {setupSteps.map((step) => (
            <Card key={step.id} className="border">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <div className="mt-1">
                      {getStatusIcon(step.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">{step.title}</h3>
                        {getStatusBadge(step.status)}
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {step.description}
                      </p>
                      
                      {step.id === 1 && (
                        <div className="space-y-2">
                          <p className="text-xs text-muted-foreground">
                            1. Go to your Supabase Dashboard → SQL Editor
                          </p>
                          <p className="text-xs text-muted-foreground">
                            2. Copy the SQL script below and paste it into the editor
                          </p>
                          <p className="text-xs text-muted-foreground">
                            3. Click "Run" to execute the script
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    {step.id === 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(step.sqlScript!, step.id)}
                        className="flex items-center gap-2"
                      >
                        {copiedStep === step.id ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                        {copiedStep === step.id ? 'Copied!' : 'Copy SQL'}
                      </Button>
                    )}
                    
                    {step.id === 2 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open('/settings', '_blank')}
                        className="flex items-center gap-2"
                      >
                        <ExternalLink className="h-3 w-3" />
                        Settings
                      </Button>
                    )}
                    
                    {step.id === 3 && onSetupComplete && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onSetupComplete}
                        className="flex items-center gap-2"
                      >
                        <FileText className="h-3 w-3" />
                        Continue
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Alert>
          <Database className="h-4 w-4" />
          <AlertDescription>
            <strong>Need Help?</strong> If you encounter any issues during setup, please check the 
            REFERENCE_QUOTE_LEARNING_SYSTEM.md documentation or contact support.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default SetupNotification;
