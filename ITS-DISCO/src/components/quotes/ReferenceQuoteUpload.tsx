// Reference Quote Upload Component

import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Upload, 
  FileText, 
  Image, 
  FileSpreadsheet, 
  File, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Brain
} from 'lucide-react';
import { uploadReferenceQuote } from '@/services/referenceQuoteService';
import { QuoteUploadRequest } from '@/types/referenceQuoteTypes';

interface ReferenceQuoteUploadProps {
  onUploadComplete?: (quoteId: string) => void;
  onUploadError?: (error: string) => void;
}

const SUPPORTED_FILE_TYPES = [
  '.pdf', '.doc', '.docx', '.txt',
  '.csv', '.xlsx', '.xls',
  '.jpg', '.jpeg', '.png', '.gif', '.bmp',
  '.zip', '.rar'
];

const PROJECT_TYPES = [
  'Residential New Construction',
  'Residential Renovation',
  'Commercial Office',
  'Commercial Retail',
  'Hospitality',
  'Healthcare',
  'Education',
  'Multi-Family',
  'Other'
];

const ReferenceQuoteUpload: React.FC<ReferenceQuoteUploadProps> = ({
  onUploadComplete,
  onUploadError
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [title, setTitle] = useState('');
  const [projectType, setProjectType] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [notes, setNotes] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploadStatus, setUploadStatus] = useState<Record<string, 'pending' | 'uploading' | 'success' | 'error'>>({});
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Check file size (50MB limit for quotes)
    const MAX_FILE_SIZE = 50 * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File "${file.name}" is too large. Maximum size is 50MB.`
      };
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!SUPPORTED_FILE_TYPES.includes(fileExtension)) {
      return {
        isValid: false,
        error: `File type "${fileExtension}" is not supported.`
      };
    }

    return { isValid: true };
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files?.length) return;

    const newFiles = Array.from(files);
    const validFiles: File[] = [];
    const errors: string[] = [];

    newFiles.forEach(file => {
      const validation = validateFile(file);
      if (validation.isValid) {
        validFiles.push(file);
      } else {
        errors.push(validation.error!);
      }
    });

    if (errors.length > 0) {
      toast({
        title: 'File Validation Error',
        description: errors.join('\n'),
        variant: 'destructive'
      });
    }

    if (validFiles.length > 0) {
      setSelectedFiles(prev => [...prev, ...validFiles]);
      
      // Initialize status for new files
      const newStatus: Record<string, 'pending'> = {};
      validFiles.forEach(file => {
        newStatus[file.name] = 'pending';
      });
      setUploadStatus(prev => ({ ...prev, ...newStatus }));
    }

    // Clear input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (fileName: string) => {
    setSelectedFiles(prev => prev.filter(file => file.name !== fileName));
    setUploadStatus(prev => {
      const newStatus = { ...prev };
      delete newStatus[fileName];
      return newStatus;
    });
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[fileName];
      return newProgress;
    });
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags(prev => [...prev, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      toast({
        title: 'No Files Selected',
        description: 'Please select at least one file to upload.',
        variant: 'destructive'
      });
      return;
    }

    if (!title.trim()) {
      toast({
        title: 'Title Required',
        description: 'Please provide a title for this quote upload.',
        variant: 'destructive'
      });
      return;
    }

    setIsUploading(true);

    try {
      const uploadPromises = selectedFiles.map(async (file) => {
        setUploadStatus(prev => ({ ...prev, [file.name]: 'uploading' }));
        setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));

        const uploadRequest: QuoteUploadRequest = {
          file,
          title: selectedFiles.length === 1 ? title : `${title} - ${file.name}`,
          projectType: projectType || undefined,
          tags: tags.length > 0 ? tags : undefined,
          notes: notes || undefined
        };

        try {
          // Simulate progress for user feedback
          const progressInterval = setInterval(() => {
            setUploadProgress(prev => ({
              ...prev,
              [file.name]: Math.min((prev[file.name] || 0) + 10, 90)
            }));
          }, 200);

          const result = await uploadReferenceQuote(uploadRequest);

          clearInterval(progressInterval);
          setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));

          if (result.success) {
            setUploadStatus(prev => ({ ...prev, [file.name]: 'success' }));
            if (onUploadComplete && result.quoteId) {
              onUploadComplete(result.quoteId);
            }
            return { success: true, fileName: file.name, quoteId: result.quoteId };
          } else {
            setUploadStatus(prev => ({ ...prev, [file.name]: 'error' }));
            if (onUploadError) {
              onUploadError(result.error || 'Upload failed');
            }
            return { success: false, fileName: file.name, error: result.error };
          }
        } catch (error) {
          setUploadStatus(prev => ({ ...prev, [file.name]: 'error' }));
          const errorMessage = error instanceof Error ? error.message : 'Upload failed';
          if (onUploadError) {
            onUploadError(errorMessage);
          }
          return { success: false, fileName: file.name, error: errorMessage };
        }
      });

      const results = await Promise.all(uploadPromises);
      
      const successCount = results.filter(r => r.success).length;
      const errorCount = results.filter(r => !r.success).length;

      if (successCount > 0) {
        toast({
          title: 'Upload Complete',
          description: `Successfully uploaded ${successCount} file${successCount !== 1 ? 's' : ''}. AI analysis will begin shortly.`,
        });

        // Reset form on successful upload
        if (errorCount === 0) {
          setSelectedFiles([]);
          setTitle('');
          setProjectType('');
          setTags([]);
          setNotes('');
          setUploadStatus({});
          setUploadProgress({});
        }
      }

      if (errorCount > 0) {
        toast({
          title: 'Upload Errors',
          description: `${errorCount} file${errorCount !== 1 ? 's' : ''} failed to upload. Please try again.`,
          variant: 'destructive'
        });
      }

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload Failed',
        description: 'An unexpected error occurred during upload.',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    if (['pdf', 'doc', 'docx', 'txt'].includes(extension || '')) {
      return <FileText className="h-4 w-4" />;
    } else if (['csv', 'xlsx', 'xls'].includes(extension || '')) {
      return <FileSpreadsheet className="h-4 w-4" />;
    } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extension || '')) {
      return <Image className="h-4 w-4" />;
    } else {
      return <File className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Upload Reference Quotes
        </CardTitle>
        <CardDescription>
          Upload previous project quotes to enhance AI recommendations and cost estimation.
          Supported formats: PDF, Word, Excel, CSV, Images, and ZIP files.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* File Upload Area */}
        <div className="space-y-4">
          <Label>Select Files</Label>
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={handleFileSelect}
              accept={SUPPORTED_FILE_TYPES.join(',')}
            />
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-2"
              disabled={isUploading}
            >
              <Upload className="h-4 w-4" />
              Choose Files
            </Button>
            <p className="text-sm text-muted-foreground mt-2">
              Select multiple quote files (PDF, Word, Excel, CSV, Images)
            </p>
            <p className="text-xs text-muted-foreground">
              Maximum file size: 50MB per file
            </p>
          </div>
        </div>

        {/* Selected Files */}
        {selectedFiles.length > 0 && (
          <div className="space-y-2">
            <Label>Selected Files ({selectedFiles.length})</Label>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {selectedFiles.map((file) => (
                <div
                  key={file.name}
                  className="flex items-center justify-between p-2 border rounded-lg"
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    {getFileIcon(file.name)}
                    <span className="text-sm truncate">{file.name}</span>
                    <span className="text-xs text-muted-foreground">
                      ({(file.size / 1024 / 1024).toFixed(1)} MB)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(uploadStatus[file.name])}
                    {uploadStatus[file.name] === 'uploading' && (
                      <span className="text-xs text-muted-foreground">
                        {uploadProgress[file.name]}%
                      </span>
                    )}
                    {!isUploading && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.name)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quote Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="e.g., Smith Residence - Living Room AV"
              disabled={isUploading}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="projectType">Project Type</Label>
            <Select value={projectType} onValueChange={setProjectType} disabled={isUploading}>
              <SelectTrigger>
                <SelectValue placeholder="Select project type" />
              </SelectTrigger>
              <SelectContent>
                {PROJECT_TYPES.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <Label>Tags</Label>
          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add tag..."
              onKeyPress={(e) => e.key === 'Enter' && addTag()}
              disabled={isUploading}
            />
            <Button onClick={addTag} variant="outline" disabled={isUploading}>
              Add
            </Button>
          </div>
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Notes */}
        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Additional notes about this quote or project..."
            rows={3}
            disabled={isUploading}
          />
        </div>

        {/* Upload Button */}
        <Button
          onClick={handleUpload}
          disabled={selectedFiles.length === 0 || !title.trim() || isUploading}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="h-4 w-4 mr-2" />
              Upload & Analyze
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export default ReferenceQuoteUpload;
