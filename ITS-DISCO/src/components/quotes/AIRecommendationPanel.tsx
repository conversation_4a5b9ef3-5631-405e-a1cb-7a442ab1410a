// AI Recommendation Panel Component

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { 
  Brain, 
  CheckCircle, 
  X, 
  ThumbsUp, 
  ThumbsDown, 
  MessageSquare,
  TrendingUp,
  DollarSign,
  Settings,
  Lightbulb,
  AlertTriangle,
  Info
} from 'lucide-react';
import { 
  AIRecommendation, 
  RecommendationData 
} from '@/types/referenceQuoteTypes';
import { 
  getWalkthroughRecommendations, 
  updateRecommendationStatus 
} from '@/services/aiRecommendationService';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface AIRecommendationPanelProps {
  walkthroughId: string;
  stepContext: string;
  roomContext?: string;
  onRecommendationAccepted?: (recommendation: AIRecommendation) => void;
  onRecommendationDismissed?: (recommendation: AIRecommendation) => void;
}

const AIRecommendationPanel: React.FC<AIRecommendationPanelProps> = ({
  walkthroughId,
  stepContext,
  roomContext,
  onRecommendationAccepted,
  onRecommendationDismissed
}) => {
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedRecommendations, setExpandedRecommendations] = useState<Set<string>>(new Set());
  const [feedbackText, setFeedbackText] = useState<Record<string, string>>({});
  const [processingActions, setProcessingActions] = useState<Set<string>>(new Set());
  
  const { toast } = useToast();

  useEffect(() => {
    loadRecommendations();
  }, [walkthroughId, stepContext, roomContext]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      const recs = await getWalkthroughRecommendations(walkthroughId, stepContext, roomContext);
      setRecommendations(recs);
    } catch (error) {
      console.error('Error loading recommendations:', error);
      toast({
        title: 'Error',
        description: 'Failed to load AI recommendations.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptRecommendation = async (recommendation: AIRecommendation) => {
    try {
      setProcessingActions(prev => new Set(prev).add(recommendation.id));
      
      const success = await updateRecommendationStatus(
        recommendation.id,
        'accepted',
        feedbackText[recommendation.id]
      );
      
      if (success) {
        setRecommendations(prev => 
          prev.map(rec => 
            rec.id === recommendation.id 
              ? { ...rec, status: 'accepted' }
              : rec
          )
        );
        
        toast({
          title: 'Recommendation Accepted',
          description: 'The AI recommendation has been applied to your walkthrough.',
        });
        
        if (onRecommendationAccepted) {
          onRecommendationAccepted(recommendation);
        }
      }
    } catch (error) {
      console.error('Error accepting recommendation:', error);
      toast({
        title: 'Error',
        description: 'Failed to accept recommendation.',
        variant: 'destructive'
      });
    } finally {
      setProcessingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(recommendation.id);
        return newSet;
      });
    }
  };

  const handleDismissRecommendation = async (recommendation: AIRecommendation) => {
    try {
      setProcessingActions(prev => new Set(prev).add(recommendation.id));
      
      const success = await updateRecommendationStatus(
        recommendation.id,
        'dismissed',
        feedbackText[recommendation.id]
      );
      
      if (success) {
        setRecommendations(prev => 
          prev.filter(rec => rec.id !== recommendation.id)
        );
        
        toast({
          title: 'Recommendation Dismissed',
          description: 'The AI recommendation has been dismissed.',
        });
        
        if (onRecommendationDismissed) {
          onRecommendationDismissed(recommendation);
        }
      }
    } catch (error) {
      console.error('Error dismissing recommendation:', error);
      toast({
        title: 'Error',
        description: 'Failed to dismiss recommendation.',
        variant: 'destructive'
      });
    } finally {
      setProcessingActions(prev => {
        const newSet = new Set(prev);
        newSet.delete(recommendation.id);
        return newSet;
      });
    }
  };

  const toggleExpanded = (recommendationId: string) => {
    setExpandedRecommendations(prev => {
      const newSet = new Set(prev);
      if (newSet.has(recommendationId)) {
        newSet.delete(recommendationId);
      } else {
        newSet.add(recommendationId);
      }
      return newSet;
    });
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'system_suggestion':
        return <Settings className="h-4 w-4" />;
      case 'product_combination':
        return <Lightbulb className="h-4 w-4" />;
      case 'cost_optimization':
        return <DollarSign className="h-4 w-4" />;
      case 'configuration_option':
        return <TrendingUp className="h-4 w-4" />;
      default:
        return <Brain className="h-4 w-4" />;
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 animate-pulse" />
            Loading AI Recommendations...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (recommendations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Recommendations
          </CardTitle>
          <CardDescription>
            No recommendations available for this step. Upload reference quotes to enable AI suggestions.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          AI Recommendations ({recommendations.length})
        </CardTitle>
        <CardDescription>
          Based on analysis of similar projects, here are some suggestions to enhance your walkthrough.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {recommendations.map((recommendation) => (
          <Card key={recommendation.id} className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <div className="mt-1">
                    {getRecommendationIcon(recommendation.recommendationType)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-base">
                      {recommendation.recommendationData.title}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {recommendation.recommendationData.description}
                    </CardDescription>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="text-xs">
                        {recommendation.recommendationType.replace('_', ' ')}
                      </Badge>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getConfidenceColor(recommendation.confidenceScore)}`}
                      >
                        {Math.round(recommendation.confidenceScore * 100)}% confidence
                      </Badge>
                      {recommendation.recommendationData.costImpact && (
                        <Badge 
                          variant={recommendation.recommendationData.costImpact.estimated < 0 ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {recommendation.recommendationData.costImpact.estimated < 0 ? 'Saves' : 'Adds'} {formatCurrency(Math.abs(recommendation.recommendationData.costImpact.estimated))}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleExpanded(recommendation.id)}
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <Collapsible 
              open={expandedRecommendations.has(recommendation.id)}
              onOpenChange={() => toggleExpanded(recommendation.id)}
            >
              <CollapsibleContent>
                <CardContent className="pt-0 space-y-4">
                  {/* Suggested Action */}
                  <div>
                    <h4 className="font-medium text-sm mb-2">Suggested Action</h4>
                    <p className="text-sm text-muted-foreground">
                      {recommendation.recommendationData.suggestedAction}
                    </p>
                  </div>

                  {/* Benefits */}
                  {recommendation.recommendationData.benefits && (
                    <div>
                      <h4 className="font-medium text-sm mb-2">Benefits</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {recommendation.recommendationData.benefits.map((benefit, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Considerations */}
                  {recommendation.recommendationData.considerations && (
                    <div>
                      <h4 className="font-medium text-sm mb-2">Considerations</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {recommendation.recommendationData.considerations.map((consideration, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <AlertTriangle className="h-3 w-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                            {consideration}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Reasoning */}
                  <div>
                    <h4 className="font-medium text-sm mb-2">Why This Recommendation?</h4>
                    <p className="text-sm text-muted-foreground">
                      {recommendation.reasoning}
                    </p>
                    {recommendation.sourceQuotes && recommendation.sourceQuotes.length > 0 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Based on {recommendation.sourceQuotes.length} similar project{recommendation.sourceQuotes.length !== 1 ? 's' : ''}
                      </p>
                    )}
                  </div>

                  {/* Feedback */}
                  <div>
                    <h4 className="font-medium text-sm mb-2">Feedback (Optional)</h4>
                    <Textarea
                      placeholder="Share your thoughts on this recommendation..."
                      value={feedbackText[recommendation.id] || ''}
                      onChange={(e) => setFeedbackText(prev => ({
                        ...prev,
                        [recommendation.id]: e.target.value
                      }))}
                      rows={2}
                      className="text-sm"
                    />
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2 border-t">
                    <Button
                      onClick={() => handleAcceptRecommendation(recommendation)}
                      disabled={processingActions.has(recommendation.id)}
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <ThumbsUp className="h-3 w-3" />
                      Accept
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleDismissRecommendation(recommendation)}
                      disabled={processingActions.has(recommendation.id)}
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <ThumbsDown className="h-3 w-3" />
                      Dismiss
                    </Button>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
};

export default AIRecommendationPanel;
