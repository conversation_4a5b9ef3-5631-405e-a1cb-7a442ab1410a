
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FormProvider } from '../context/FormContext';
import FormProgress from './FormProgress';
import ClientInfoStep from './steps/ClientInfoStep';
import QualificationStep from './steps/QualificationStep';
import SystemSelectionStep from './steps/SystemSelectionStep';
import RoomWalkthroughStep from './steps/RoomWalkthroughStep';
import FilesUploadStep from './steps/FilesUploadStep';
import SummaryStep from './steps/SummaryStep';
import FinalActionsStep from './steps/FinalActionsStep';
import SaveWalkthrough from './SaveWalkthrough';
import SavedWalkthroughs from './SavedWalkthroughs';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, X, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Walkthrough } from '@/services/walkthroughService';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import ErrorBoundary from './ErrorBoundary';

interface ClientWalkthroughProps {
  initialClientInfo?: any;
  initialWalkthroughId?: string;
  initialWalkthroughData?: Walkthrough;
  onWalkthroughFinish?: () => void;
  startAtQualification?: boolean;
}

const ClientWalkthrough: React.FC<ClientWalkthroughProps> = ({
  initialClientInfo,
  initialWalkthroughId,
  initialWalkthroughData,
  onWalkthroughFinish,
  startAtQualification = false
}) => {
  const [currentWalkthroughId, setCurrentWalkthroughId] = useState<string | null>(
    initialWalkthroughId || null
  );

  return (
    <FormProvider
      initialClientInfo={initialClientInfo}
      initialWalkthroughData={initialWalkthroughData}
      startAtQualification={startAtQualification}
    >
      <ClientWalkthroughContent
        initialClientInfo={initialClientInfo}
        initialWalkthroughId={initialWalkthroughId}
        initialWalkthroughData={initialWalkthroughData}
        onWalkthroughFinish={onWalkthroughFinish}
        currentWalkthroughId={currentWalkthroughId}
        setCurrentWalkthroughId={setCurrentWalkthroughId}
        startAtQualification={startAtQualification}
      />
    </FormProvider>
  );
};

// Content component that uses the form context
const ClientWalkthroughContent: React.FC<ClientWalkthroughProps & {
  currentWalkthroughId: string | null;
  setCurrentWalkthroughId: React.Dispatch<React.SetStateAction<string | null>>;
}> = ({
  initialClientInfo,
  initialWalkthroughData,
  onWalkthroughFinish,
  initialWalkthroughId,
  currentWalkthroughId,
  setCurrentWalkthroughId
}) => {
  // Get form context after it's been properly initialized by the parent FormProvider
  const {
    formData,
    loadSavedWalkthrough,
    resetForm
  } = useFormContext();

  const { currentStep } = formData;
  const { toast } = useToast();
  const [showNavHint, setShowNavHint] = useState(true);
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const initialLoadRef = useRef(false);

  // Add logging for current step
  useEffect(() => {
    console.log("ClientWalkthroughContent: Current step is", currentStep || 1);
  }, [currentStep]);

  // Load the walkthrough if initialWalkthroughId is provided (editing mode)
  useEffect(() => {
    // Prevent multiple loading attempts for the same walkthrough
    if (initialWalkthroughId && !currentWalkthroughId && !initialLoadRef.current) {
      initialLoadRef.current = true;
      loadSavedWalkthrough(initialWalkthroughId).then(success => {
        if (success) {
          setCurrentWalkthroughId(initialWalkthroughId);
        }
      });
    }
  }, [initialWalkthroughId, currentWalkthroughId, loadSavedWalkthrough, setCurrentWalkthroughId]);

  const handleFinishWalkthrough = useCallback(() => {
    console.log("ClientWalkthroughContent: handleFinishWalkthrough called");
    if (onWalkthroughFinish) {
      console.log("ClientWalkthroughContent: Executing parent callback");
      onWalkthroughFinish();
    } else {
      console.log("ClientWalkthroughContent: No finish callback provided, navigating manually");
      toast({
        title: "Walkthrough Completed",
        description: "Your client walkthrough has been successfully completed."
      });

      // Use navigate instead of a full page reload
      navigate('/clients', { replace: true });
    }
  }, [onWalkthroughFinish, navigate, toast]);

  const renderStep = () => {
    console.log("Rendering step:", currentStep);

    // Ensure we use a 1-based index for steps (1 to 7)
    // Default to step 1 if currentStep is undefined or 0
    const safeCurrentStep = currentStep && currentStep > 0 ? currentStep : 1;

    switch (safeCurrentStep) {
      case 1:
        return <ClientInfoStep />;
      case 2:
        return <QualificationStep />;
      case 3:
        return <SystemSelectionStep />;
      case 4:
        return <RoomWalkthroughStep />;
      case 5:
        return <FilesUploadStep />;
      case 6:
        return <SummaryStep />;
      case 7:
        console.log("Rendering FinalActionsStep with handleFinishWalkthrough callback");
        return <FinalActionsStep
          onWalkthroughFinish={handleFinishWalkthrough}
          currentWalkthroughId={currentWalkthroughId}
          onWalkthroughSaved={(id) => setCurrentWalkthroughId(id)}
        />;
      default:
        console.log("Default case - rendering ClientInfoStep");
        return <ClientInfoStep />;
    }
  };

  const handleLoadWalkthrough = async (id: string) => {
    try {
      const success = await loadSavedWalkthrough(id);

      if (success) {
        toast({
          title: "Success",
          description: "Walkthrough loaded successfully",
        });
      } else {
        throw new Error("Failed to load walkthrough");
      }
    } catch (error) {
      console.error("Error loading walkthrough:", error);
      toast({
        title: "Error",
        description: "Failed to load walkthrough",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto px-3 md:px-4 py-4 md:py-6 bg-card rounded-lg shadow-sm">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-3 mb-4 md:mb-6">
        <h1 className="text-xl md:text-2xl font-bold">
          {initialWalkthroughData ? `Edit Client: ${initialWalkthroughData.client_name}` : 'New Client Walkthrough'}
        </h1>
        <div className="flex flex-wrap gap-2">
          <SavedWalkthroughs onLoad={handleLoadWalkthrough} />
          <SaveWalkthrough
            currentId={currentWalkthroughId}
            onSaved={(id) => setCurrentWalkthroughId(id)}
          />
        </div>
      </div>

      {showNavHint && (
        <Alert className="mb-4 md:mb-6 pr-12 relative" variant="default">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-xs md:text-sm">
            Navigate through completed steps by clicking on the progress indicators above.
          </AlertDescription>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowNavHint(false)}
            className="absolute right-1 top-1 h-6 w-6 p-0 rounded-full"
            aria-label="Dismiss"
          >
            <X className="h-3 w-3" />
          </Button>
        </Alert>
      )}

      <FormProgress />
      <ErrorBoundary>
        <div className="animate-fade-in">
          {renderStep()}
        </div>
      </ErrorBoundary>
    </div>
  );
};

// Import at the end to avoid issues with context initialization
import { useFormContext } from '../context/FormContext';

export default ClientWalkthrough;
