import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Home, Settings, LogOut, LogIn, Send, ArrowUpFromLine, User, PackageOpen } from 'lucide-react';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/theme/ThemeToggle';
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { UserProfile } from "@/types/userTypes";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);

  // Fetch user profile data when user changes
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user?.id) return;
      
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .maybeSingle();
          
        if (error) throw error;
        
        if (data) {
          setProfile(data);
          
          if (data.avatar_url) {
            const { data: avatarData } = await supabase
              .storage
              .from('avatars')
              .download(`${user.id}/${data.avatar_url}`);
              
            if (avatarData) {
              const url = URL.createObjectURL(avatarData);
              setAvatarUrl(url);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };
    
    fetchProfile();
  }, [user]);

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name.charAt(0)}${profile.last_name.charAt(0)}`;
    }
    
    if (profile?.first_name) {
      return profile.first_name.charAt(0).toUpperCase();
    }
    
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    
    return "U";
  };

  return (
    <header className="bg-background border-b border-border py-1 px-2 sm:py-2 sm:px-4 sticky top-0 z-10">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center gap-1 sm:gap-2">
          <SidebarTrigger className="md:hidden" />
          <img 
            src="/lovable-uploads/b236b3b0-3175-4672-8fad-ee4e29a85315.png" 
            alt="Logo" 
            className="h-9 w-9 sm:h-12 sm:w-12"
          />
          <h1 className="text-base sm:text-xl font-bold hidden sm:block">Innovative Client Discovery</h1>
        </div>
        
        <div className="hidden md:flex">
          <NavigationMenu>
            <NavigationMenuList>
              <NavigationMenuItem>
                <Link to="/" className={cn(navigationMenuTriggerStyle(), "cursor-pointer")}>
                  Home
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <NavigationMenuTrigger>Services</NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
                    <li className="row-span-3">
                      <NavigationMenuLink asChild>
                        <a
                          className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md"
                          href="/"
                          onClick={(e) => {
                            e.preventDefault();
                            navigate('/');
                          }}
                        >
                          <div className="mb-2 mt-4 text-lg font-medium">
                            Infrastructure TakeOff
                          </div>
                          <p className="text-sm leading-tight text-muted-foreground">
                            Complete infrastructure evaluation and cost estimation for your projects.
                          </p>
                        </a>
                      </NavigationMenuLink>
                    </li>
                    <li>
                      <Link to="/walkthrough" className={cn("block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground")}>
                        <div className="text-sm font-medium leading-none">New Walkthrough</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Start a new client walkthrough process
                        </p>
                      </Link>
                    </li>
                    <li>
                      <Link to="/clients" className={cn("block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground")}>
                        <div className="text-sm font-medium leading-none">Client Management</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Manage your client database
                        </p>
                      </Link>
                    </li>
                    <li>
                      <Link to="/actions" className={cn("block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground")}>
                        <div className="text-sm font-medium leading-none">Walkthrough Sync</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Sync walkthrough data to external systems
                        </p>
                      </Link>
                    </li>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/products" className={cn(navigationMenuTriggerStyle(), "cursor-pointer flex items-center gap-1")}>
                  <PackageOpen className="h-4 w-4" />
                  Products
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/actions" className={cn(navigationMenuTriggerStyle(), "cursor-pointer flex items-center gap-1")}>
                  <ArrowUpFromLine className="h-4 w-4" />
                  Sync Data
                </Link>
              </NavigationMenuItem>
              <NavigationMenuItem>
                <Link to="/settings" className={cn(navigationMenuTriggerStyle(), "cursor-pointer")}>
                  Settings
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        </div>
        
        <div className="flex gap-1 sm:gap-2 items-center">
          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="rounded-full p-0 h-8 w-8 sm:h-9 sm:w-9">
                  <Avatar className="h-8 w-8 sm:h-9 sm:w-9">
                    <AvatarImage src={avatarUrl || ""} alt={profile?.first_name || "User"} />
                    <AvatarFallback>{getUserInitials()}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>
                  {profile?.first_name || user.email}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => navigate('/profile')}>
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/settings')}>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button variant="ghost" size="sm" className="px-2 sm:px-3" onClick={() => navigate('/auth')}>
              <LogIn className="h-4 w-4 sm:mr-2" />
              <span className="hidden sm:inline">Sign In</span>
            </Button>
          )}
          <ThemeToggle />
          <Button variant="ghost" size="icon" onClick={() => navigate('/')} className="sm:hidden">
            <Home className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => navigate('/settings')} className="sm:hidden">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;
