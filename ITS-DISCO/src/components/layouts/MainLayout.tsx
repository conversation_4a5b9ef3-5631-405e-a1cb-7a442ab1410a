
import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from '@/components/Header';
import { SidebarProvider, Sidebar, SidebarContent, SidebarHeader, SidebarFooter, SidebarGroup, SidebarGroupLabel, SidebarGroupContent, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarTrigger, SidebarRail, SidebarInset } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Home, Settings, FileText, Users, PlusCircle, DollarSign, PanelLeftClose, PanelLeft, ArrowUpFromLine, PackageOpen, User, LogOut } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { SimpleThemeToggle } from '@/components/theme/ThemeToggle';
import { useIsMobile } from '@/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';

// Custom sidebar toggle button that shows the current state
const SidebarToggle = () => {
  const { toggleSidebar, open } = useSidebar();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleSidebar}
      className="h-8 w-8 transition-all duration-200"
      title={open ? "Collapse sidebar" : "Expand sidebar"}
    >
      {open ? (
        <PanelLeftClose className="h-5 w-5" />
      ) : (
        <PanelLeft className="h-5 w-5" />
      )}
      <span className="sr-only">{open ? "Collapse" : "Expand"} sidebar</span>
    </Button>
  );
};

const MainLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();
  const { user, signOut } = useAuth();
  const { toast } = useToast();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: "Signed out successfully",
        description: "You have been logged out of your account.",
      });
      navigate('/auth');
    } catch (error) {
      toast({
        title: "Error signing out",
        description: "There was a problem signing you out. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getUserInitials = () => {
    if (user?.email) {
      return user.email.substring(0, 2).toUpperCase();
    }
    return 'U';
  };

  const getUserDisplayName = () => {
    if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'User';
  };

  return (
    <SidebarProvider defaultOpen={!isMobile}>
      <div className="min-h-screen flex w-full">
        <Sidebar>
          <SidebarHeader className="flex items-center px-4 py-2">
            <img src="/lovable-uploads/b236b3b0-3175-4672-8fad-ee4e29a85315.png" alt="Logo" className="h-8 w-8 md:h-10 md:w-10 mr-2" />
            <span className="text-base md:text-lg font-bold hidden md:block">Innovative Discovery</span>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Navigation</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="Home" isActive={isActive('/')} onClick={() => navigate('/')}>
                      <Home className="h-5 w-5" />
                      <span>Home</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="New Walkthrough" isActive={isActive('/walkthrough') || isActive('/new-walkthrough')} onClick={() => navigate('/walkthrough')}>
                      <PlusCircle className="h-5 w-5" />
                      <span>New Walkthrough</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="Products" isActive={isActive('/products')} onClick={() => navigate('/products')}>
                      <PackageOpen className="h-5 w-5" />
                      <span>Products</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="Cost Manager" isActive={isActive('/cost-manager')} onClick={() => navigate('/cost-manager')}>
                      <DollarSign className="h-5 w-5" />
                      <span>Cost Manager</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="Reports" isActive={isActive('/reports')} onClick={() => navigate('/reports')}>
                      <FileText className="h-5 w-5" />
                      <span>Reports</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="Clients List" isActive={isActive('/clients')} onClick={() => navigate('/clients')}>
                      <Users className="h-5 w-5" />
                      <span>Clients List</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="Data Sync" isActive={isActive('/actions')} onClick={() => navigate('/actions')}>
                      <ArrowUpFromLine className="h-5 w-5" />
                      <span>Data Sync</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>

                  <SidebarMenuItem>
                    <SidebarMenuButton tooltip="Settings" isActive={isActive('/settings')} onClick={() => navigate('/settings')}>
                      <Settings className="h-5 w-5" />
                      <span>Settings</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>


          </SidebarContent>
          <SidebarFooter className="p-3 md:p-4 space-y-3">
            {/* User Profile Section */}
            <div className="flex items-center gap-3 px-2 py-2 rounded-lg bg-sidebar-accent/50">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.user_metadata?.avatar_url} />
                <AvatarFallback className="text-xs font-medium">
                  {getUserInitials()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0 hidden md:block">
                <p className="text-sm font-medium truncate">
                  {getUserDisplayName()}
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  {user?.email}
                </p>
              </div>
            </div>

            {/* Theme Toggle */}
            <div className="flex items-center justify-between px-2">
              <span className="text-xs md:text-sm text-muted-foreground hidden md:block">Theme</span>
              <SimpleThemeToggle />
            </div>

            {/* Logout Button */}
            <div className="px-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSignOut}
                className="w-full justify-start text-muted-foreground hover:text-foreground hover:bg-sidebar-accent"
              >
                <LogOut className="h-4 w-4 mr-2" />
                <span className="hidden md:inline">Sign Out</span>
              </Button>
            </div>
          </SidebarFooter>
          <SidebarRail />
        </Sidebar>

        <SidebarInset className="flex flex-col w-full">
          <div className="sticky top-0 z-10 bg-background border-b border-border w-full">
            <div className="p-2">
              <div className="flex items-center gap-2">
                {/* Desktop custom sidebar toggle */}
                <div className="hidden md:block">
                  <SidebarToggle />
                </div>
                {/* Mobile default sidebar toggle */}
                <div className="md:hidden">
                  <SidebarTrigger />
                </div>
                <div className="flex items-center md:hidden">
                  <img
                    src="/lovable-uploads/b236b3b0-3175-4672-8fad-ee4e29a85315.png"
                    alt="Logo"
                    className="h-7 w-7"
                  />
                  <span className="text-sm font-bold ml-2">Innovative Discovery</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex-1 overflow-auto safe-area-inset">
            <Outlet />
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
