
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit, Zap, Loader2, Settings, Key } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { useFormContext } from '../../../context/FormContext';
import { generateRecommendations, getOpenAIKey, OpenAIResponse } from '@/services/openaiService';

interface ExecutiveSummaryCardProps {
  executiveSummary: string;
  onUpdate: (summary: string) => void;
}

const ExecutiveSummaryCard: React.FC<ExecutiveSummaryCardProps> = ({ executiveSummary, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempSummary, setTempSummary] = useState(executiveSummary || "");
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { formData } = useFormContext();

  const apiKey = getOpenAIKey();

  const handleSave = () => {
    onUpdate(tempSummary);
    setIsEditing(false);
    toast({
      title: "Summary Updated",
      description: "Executive summary has been updated",
    });
  };

  const handleRunExecutiveSummary = async () => {
    // Check if API key is configured
    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Redirecting to Settings to configure your OpenAI API key...",
        variant: "destructive",
      });
      // Redirect to Settings with AI tab
      navigate('/settings');
      return;
    }

    setIsGenerating(true);

    try {
      const { clientInfo, qualificationInfo, systemCategories, rooms } = formData;

      const selectedSystems = Object.entries(systemCategories)
        .filter(([_, value]) => value)
        .map(([key]) => key);

      const systemPrompt = "You are a professional technical writer for a high-end home automation company. Create a concise yet comprehensive executive summary of the client walkthrough information that can be presented to the client.";

      const userPrompt = `Generate a professional executive summary of the following home automation walkthrough for a high-end client:

Client: ${clientInfo.fullName}
Project Address: ${clientInfo.address}
Project Type: ${clientInfo.projectType}
Home Type: ${clientInfo.homeType}
Budget Range: $${qualificationInfo.budget.toLocaleString()}
Square Footage: ${qualificationInfo.squareFootage.toLocaleString()} sq ft
Number of Floors: ${qualificationInfo.floors}
Project Stage: ${qualificationInfo.projectStage}
Target Completion: ${formatDate(qualificationInfo.completionDate)}
${clientInfo.architectInfo ? `Architect/Designer: ${clientInfo.architectInfo}` : ''}

Selected Systems: ${selectedSystems.join(', ')}

Room Details:
${rooms.map(room => `
- ${room.name}:
  ${room.lighting ? '✓ Lighting Control' : ''}
  ${room.shades !== 'none' ? `✓ ${room.shades} Shades` : ''}
  ${room.audio !== 'none' ? `✓ ${room.audio} Audio` : ''}
  ${room.video !== 'none' ? `✓ ${room.video} Video` : ''}
  ${room.surveillance ? '✓ Surveillance' : ''}
  ${room.control !== 'none' ? `✓ ${room.control} Control` : ''}
  ${room.notes ? `Notes: ${room.notes}` : ''}
`).join('')}

Format this as a professional executive summary that I can present to the client.`;

      const response: OpenAIResponse = await generateRecommendations(
        systemPrompt,
        userPrompt,
        apiKey
      );

      if (response.success) {
        onUpdate(response.content);
        setTempSummary(response.content);
        toast({
          title: "Executive Summary Generated",
          description: "AI-powered executive summary has been generated successfully",
        });
      } else {
        throw new Error(response.error || "Failed to generate executive summary");
      }
    } catch (error) {
      console.error("Error generating executive summary:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate executive summary. Please check your API key in Settings and try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between">
          <div>
            <CardTitle>Executive Summary</CardTitle>
            <CardDescription>AI-powered summary based on project details</CardDescription>
          </div>
          <div className="flex gap-2">
            {executiveSummary && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setTempSummary(executiveSummary || "");
                  setIsEditing(!isEditing);
                }}
                className="text-xs flex items-center gap-1"
              >
                <Edit className="h-3 w-3" />
                {isEditing ? "Cancel" : "Edit"}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            <Textarea
              value={tempSummary}
              onChange={(e) => setTempSummary(e.target.value)}
              rows={10}
              placeholder="Enter executive summary here..."
              className="font-normal"
            />
            <div className="flex justify-end">
              <Button onClick={handleSave}>
                Save Summary
              </Button>
            </div>
          </div>
        ) : (
          <>
            {!apiKey && (
              <div className="p-4 bg-muted/50 rounded-lg border border-dashed mb-4">
                <p className="text-sm text-muted-foreground text-center">
                  <Key className="h-4 w-4 inline mr-2" />
                  OpenAI API key required. Click "Run Executive Summary" to configure it in{' '}
                  <span className="text-primary font-medium">Settings</span>.
                </p>
              </div>
            )}

            <div className="space-y-4">
              <Button
                onClick={handleRunExecutiveSummary}
                disabled={isGenerating}
                className="w-full bg-[#37372f] hover:bg-[#4a4a3f] text-white"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Executive Summary...
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-4 w-4" />
                    Run Executive Summary
                  </>
                )}
              </Button>

              {executiveSummary && (
                <div className="prose prose-sm max-w-none">
                  {executiveSummary.split('\n').map((paragraph, index) => (
                    paragraph ? <p key={index} className="mb-4">{paragraph}</p> : <br key={index} />
                  ))}
                </div>
              )}

              {!executiveSummary && !isGenerating && (
                <p className="text-muted-foreground italic text-center py-8">
                  No executive summary has been generated yet. Click "Run Executive Summary" to generate one using AI.
                </p>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ExecutiveSummaryCard;

// Helper function to format dates safely
const formatDate = (date: Date | string | null): string => {
  if (!date) return "Not specified";

  try {
    // Handle both string and Date objects
    if (typeof date === 'string') {
      return new Date(date).toLocaleDateString();
    } else if (date instanceof Date) {
      return date.toLocaleDateString();
    }
    return "Invalid date";
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Date format error";
  }
};
