
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit } from 'lucide-react';
import { ClientInfo } from '@/types/formTypes';
import { useFormContext } from '@/context/FormContext';

interface ClientInfoCardProps {
  clientInfo: ClientInfo;
  onEdit: () => void;
}

const ClientInfoCard: React.FC<ClientInfoCardProps> = ({ clientInfo, onEdit }) => {
  const { formData } = useFormContext();

  // Function to format address with available components
  const formatAddress = () => {
    const addressParts = [];
    if (clientInfo.address) addressParts.push(clientInfo.address);

    const cityStateZip = [];
    if (clientInfo.city) cityStateZip.push(clientInfo.city);
    if (clientInfo.state) cityStateZip.push(clientInfo.state);
    if (clientInfo.zipCode) cityStateZip.push(clientInfo.zipCode);

    const cityStateZipStr = cityStateZip.join(', ');
    if (cityStateZipStr) addressParts.push(cityStateZipStr);

    return addressParts.length > 0 ? addressParts.join(', ') : "Not provided";
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between">
          <div>
            <CardTitle>Client Information</CardTitle>
            <CardDescription>Basic client and project details</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onEdit}
            className="text-xs flex items-center gap-1"
          >
            <Edit className="h-3 w-3" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <dt className="text-muted-foreground">Name</dt>
            <dd className="font-medium">{clientInfo.fullName || "Not provided"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Email</dt>
            <dd className="font-medium">{clientInfo.email || "Not provided"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Phone</dt>
            <dd className="font-medium">{clientInfo.phone || "Not provided"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Street Address</dt>
            <dd className="font-medium">{clientInfo.address || "Not provided"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">City</dt>
            <dd className="font-medium">{clientInfo.city || "Not provided"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">State</dt>
            <dd className="font-medium">{clientInfo.state || "Not provided"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">ZIP Code</dt>
            <dd className="font-medium">{clientInfo.zipCode || "Not provided"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">
              {formData.walkthroughType === 'commercial' ? "Commercial Type" : "Home Type"}
            </dt>
            <dd className="font-medium">{clientInfo.homeType || "Not specified"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Project Type</dt>
            <dd className="font-medium">{clientInfo.projectType || "Not specified"}</dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
};

export default ClientInfoCard;
