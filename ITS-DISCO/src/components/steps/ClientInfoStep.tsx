import React, { useState, useEffect } from 'react';
import { useFormContext } from '../../context/FormContext';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import FormNavigation from '../FormNavigation';
import { ClientInfo } from '@/types/formTypes';

const ClientInfoStep: React.FC = () => {
  const { formData, setFormData } = useFormContext();
  // Ensure we initialize with default values to prevent undefined errors
  const clientInfo = formData?.clientInfo || {
    firstName: "",
    lastName: "",
    fullName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    architectInfo: "",
    homeType: "",
    projectType: "",
  };
  const { toast } = useToast();

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [firstName, setFirstName] = useState(clientInfo.firstName || '');
  const [lastName, setLastName] = useState(clientInfo.lastName || '');

  // Initialize first and last name from fullName if it exists
  useEffect(() => {
    if (clientInfo.fullName && (!firstName && !lastName)) {
      const nameParts = clientInfo.fullName.split(' ');
      if (nameParts.length > 1) {
        setFirstName(nameParts[0]);
        setLastName(nameParts.slice(1).join(' '));
      } else {
        setFirstName(clientInfo.fullName);
        setLastName('');
      }
    }

    // Also populate firstName and lastName from clientInfo if they exist
    if (clientInfo.firstName && !firstName) {
      setFirstName(clientInfo.firstName);
    }

    if (clientInfo.lastName && !lastName) {
      setLastName(clientInfo.lastName);
    }
  }, [clientInfo.fullName, clientInfo.firstName, clientInfo.lastName, firstName, lastName]);

  // Update full name when first or last name changes
  const updateNames = (first: string, last: string) => {
    setFirstName(first);
    setLastName(last);

    const fullName = `${first} ${last}`.trim();

    setFormData((prev) => ({
      ...prev,
      clientInfo: {
        ...prev.clientInfo,
        firstName: first,
        lastName: last,
        fullName: fullName
      },
    }));
  };

  // Fix: Ensure we're using required properties in the clientInfo object
  const updateClientInfo = (key: keyof ClientInfo, value: string) => {
    setFormData((prev) => ({
      ...prev,
      clientInfo: {
        ...prev.clientInfo,
        [key]: value
      },
    }));

    // Clear error for this field if it exists
    if (errors[key]) {
      setErrors((prev) => {
        const updated = { ...prev };
        delete updated[key];
        return updated;
      });
    }
  };

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  const validatePhone = (phone: string) => {
    return /^[0-9+\-() ]{10,15}$/.test(phone);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!clientInfo.email?.trim()) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(clientInfo.email)) {
      newErrors.email = "Valid email is required";
    }

    if (!clientInfo.phone?.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!validatePhone(clientInfo.phone)) {
      newErrors.phone = "Valid phone number is required";
    }

    if (!clientInfo.address?.trim()) {
      newErrors.address = "Street address is required";
    }

    if (!clientInfo.city?.trim()) {
      newErrors.city = "City is required";
    }

    if (!clientInfo.state?.trim()) {
      newErrors.state = "State is required";
    }

    if (!clientInfo.zipCode?.trim()) {
      newErrors.zipCode = "ZIP code is required";
    }

    if (!clientInfo.projectType) {
      newErrors.projectType = "Project type is required";
    }

    // Conditional validation for homeType based on walkthrough type
    if (!clientInfo.homeType) {
      if (formData.walkthroughType === 'commercial') {
        newErrors.homeType = "Commercial type is required";
      } else {
        newErrors.homeType = "Home type is required";
      }
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      toast({
        title: "Please fix the errors",
        description: "Some required fields need your attention",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleNext = () => {
    return validateForm();
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-6">Client & Project Information</h2>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name <span className="text-red-500">*</span></Label>
            <Input
              id="firstName"
              value={firstName}
              onChange={(e) => updateNames(e.target.value, lastName)}
              placeholder="Enter first name"
              className={errors.firstName ? "border-red-500" : ""}
            />
            {errors.firstName && <p className="text-sm text-red-500">{errors.firstName}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name <span className="text-red-500">*</span></Label>
            <Input
              id="lastName"
              value={lastName}
              onChange={(e) => updateNames(firstName, e.target.value)}
              placeholder="Enter last name"
              className={errors.lastName ? "border-red-500" : ""}
            />
            {errors.lastName && <p className="text-sm text-red-500">{errors.lastName}</p>}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email Address <span className="text-red-500">*</span></Label>
          <Input
            id="email"
            type="email"
            value={clientInfo.email || ""}
            onChange={(e) => updateClientInfo("email", e.target.value)}
            placeholder="<EMAIL>"
            className={errors.email ? "border-red-500" : ""}
          />
          {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number <span className="text-red-500">*</span></Label>
          <Input
            id="phone"
            value={clientInfo.phone || ""}
            onChange={(e) => updateClientInfo("phone", e.target.value)}
            placeholder="(*************"
            className={errors.phone ? "border-red-500" : ""}
          />
          {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">Street Address <span className="text-red-500">*</span></Label>
          <Input
            id="address"
            value={clientInfo.address || ""}
            onChange={(e) => updateClientInfo("address", e.target.value)}
            placeholder="123 Main St"
            className={errors.address ? "border-red-500" : ""}
          />
          {errors.address && <p className="text-sm text-red-500">{errors.address}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="city">City <span className="text-red-500">*</span></Label>
            <Input
              id="city"
              value={clientInfo.city || ""}
              onChange={(e) => updateClientInfo("city", e.target.value)}
              placeholder="City"
              className={errors.city ? "border-red-500" : ""}
            />
            {errors.city && <p className="text-sm text-red-500">{errors.city}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="state">State <span className="text-red-500">*</span></Label>
            <Input
              id="state"
              value={clientInfo.state || ""}
              onChange={(e) => updateClientInfo("state", e.target.value)}
              placeholder="State"
              className={errors.state ? "border-red-500" : ""}
            />
            {errors.state && <p className="text-sm text-red-500">{errors.state}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="zipCode">ZIP Code <span className="text-red-500">*</span></Label>
            <Input
              id="zipCode"
              value={clientInfo.zipCode || ""}
              onChange={(e) => updateClientInfo("zipCode", e.target.value)}
              placeholder="ZIP Code"
              className={errors.zipCode ? "border-red-500" : ""}
            />
            {errors.zipCode && <p className="text-sm text-red-500">{errors.zipCode}</p>}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="architectInfo">Architect / Designer / Builder</Label>
          <Input
            id="architectInfo"
            value={clientInfo.architectInfo || ""}
            onChange={(e) => updateClientInfo("architectInfo", e.target.value)}
            placeholder="Optional - Enter name or company"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="projectType">Project Type <span className="text-red-500">*</span></Label>
            <Select
              value={clientInfo.projectType}
              onValueChange={(value: "" | "New Build" | "Renovation" | "Upgrade") => {
                updateClientInfo("projectType", value);
              }}
            >
              <SelectTrigger className={errors.projectType ? "border-red-500" : ""}>
                <SelectValue placeholder="Select project type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="New Build">New Build</SelectItem>
                <SelectItem value="Renovation">Renovation</SelectItem>
                <SelectItem value="Upgrade">Upgrade</SelectItem>
              </SelectContent>
            </Select>
            {errors.projectType && <p className="text-sm text-red-500">{errors.projectType}</p>}
          </div>

          {/* Conditional field based on walkthrough type */}
          {formData.walkthroughType === 'commercial' ? (
            <div className="space-y-2">
              <Label htmlFor="homeType">Commercial Type <span className="text-red-500">*</span></Label>
              <Select
                value={clientInfo.homeType}
                onValueChange={(value: "" | "Office" | "Retail" | "Restaurant" | "Hotel" | "Healthcare" | "Education" | "Warehouse" | "Mixed Use") =>
                  updateClientInfo("homeType", value)
                }
              >
                <SelectTrigger className={errors.homeType ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select commercial type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Office">Office</SelectItem>
                  <SelectItem value="Retail">Retail</SelectItem>
                  <SelectItem value="Restaurant">Restaurant</SelectItem>
                  <SelectItem value="Hotel">Hotel</SelectItem>
                  <SelectItem value="Healthcare">Healthcare</SelectItem>
                  <SelectItem value="Education">Education</SelectItem>
                  <SelectItem value="Warehouse">Warehouse</SelectItem>
                  <SelectItem value="Mixed Use">Mixed Use</SelectItem>
                </SelectContent>
              </Select>
              {errors.homeType && <p className="text-sm text-red-500">{errors.homeType}</p>}
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="homeType">Type of Home <span className="text-red-500">*</span></Label>
              <Select
                value={clientInfo.homeType}
                onValueChange={(value: "" | "Primary" | "Vacation" | "Rental") =>
                  updateClientInfo("homeType", value)
                }
              >
                <SelectTrigger className={errors.homeType ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select home type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Primary">Primary</SelectItem>
                  <SelectItem value="Vacation">Vacation</SelectItem>
                  <SelectItem value="Rental">Rental</SelectItem>
                </SelectContent>
              </Select>
              {errors.homeType && <p className="text-sm text-red-500">{errors.homeType}</p>}
            </div>
          )}
        </div>
      </div>

      <FormNavigation onNext={validateForm} />
    </div>
  );
};

export default ClientInfoStep;
