
import React, { useState, useRef } from 'react';
import { useFormContext } from '../../context/FormContext';
import FormNavigation from '../FormNavigation';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Upload, FileX, AlertCircle, CheckCircle2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

const FilesUploadStep: React.FC = () => {
  const { formData, setFormData } = useFormContext();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Supported file types for project files
  const SUPPORTED_FILE_TYPES = [
    '.pdf', '.doc', '.docx', '.txt', '.rtf',
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
    '.dwg', '.dxf', '.skp', '.3ds',
    '.xls', '.xlsx', '.csv',
    '.zip', '.rar', '.7z'
  ];

  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB limit

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File "${file.name}" is too large. Maximum size is 10MB.`
      };
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!SUPPORTED_FILE_TYPES.includes(fileExtension)) {
      return {
        isValid: false,
        error: `File type "${fileExtension}" is not supported. Supported types: ${SUPPORTED_FILE_TYPES.join(', ')}`
      };
    }

    return { isValid: true };
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Clear previous messages
    setUploadError(null);
    setUploadSuccess(null);

    try {
      const files = e.target.files;
      if (!files?.length) return;

      const newFiles = Array.from(files);
      const validFiles: File[] = [];
      const errors: string[] = [];

      // Validate each file
      for (const file of newFiles) {
        const validation = validateFile(file);
        if (validation.isValid) {
          validFiles.push(file);
        } else {
          errors.push(validation.error!);
        }
      }

      // Show errors if any
      if (errors.length > 0) {
        const errorMessage = errors.join('\n');
        setUploadError(errorMessage);
        toast({
          title: 'File Upload Error',
          description: errorMessage,
          variant: 'destructive'
        });
      }

      // Add valid files if any
      if (validFiles.length > 0) {
        setUploadedFiles(prev => [...prev, ...validFiles]);

        // Fix: Handle projectFiles as an object with arrays, not as a direct array
        setFormData(prev => {
          const currentProjectFiles = prev.projectFiles || { floorplans: [], references: [] };

          // Ensure projectFiles is an object with the expected structure
          const updatedProjectFiles = {
            floorplans: Array.isArray(currentProjectFiles.floorplans) ? currentProjectFiles.floorplans : [],
            references: Array.isArray(currentProjectFiles.references) ? [...currentProjectFiles.references, ...validFiles] : [...validFiles]
          };

          return {
            ...prev,
            projectFiles: updatedProjectFiles
          };
        });

        setUploadSuccess(`Successfully uploaded ${validFiles.length} file${validFiles.length !== 1 ? 's' : ''}`);
        toast({
          title: 'Files Uploaded',
          description: `Successfully uploaded ${validFiles.length} file${validFiles.length !== 1 ? 's' : ''}`,
        });
      }

      // Clear the input value so the same file can be uploaded again if needed
      e.target.value = '';
    } catch (error) {
      console.error('Error in handleFileChange:', error);
      const errorMessage = 'An unexpected error occurred while uploading files.';
      setUploadError(errorMessage);
      toast({
        title: 'Upload Error',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removeFile = (index: number) => {
    const fileToRemove = uploadedFiles[index];

    setUploadedFiles(prev => prev.filter((_, i) => i !== index));

    // Fix: Handle projectFiles as an object with arrays
    setFormData(prev => {
      const currentProjectFiles = prev.projectFiles || { floorplans: [], references: [] };

      // Remove the file from the references array (assuming all uploaded files go to references)
      const updatedReferences = Array.isArray(currentProjectFiles.references)
        ? currentProjectFiles.references.filter(file => file !== fileToRemove)
        : [];

      return {
        ...prev,
        projectFiles: {
          ...currentProjectFiles,
          references: updatedReferences
        }
      };
    });

    // Clear any previous messages
    setUploadError(null);
    setUploadSuccess(null);
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-6">Project Files</h2>
      <p className="text-muted-foreground mb-8">
        Upload any relevant project files, documents, or plans. Supported file types include PDFs, images, CAD files, and documents.
      </p>

      {/* Error Alert */}
      {uploadError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="whitespace-pre-line">
            {uploadError}
          </AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {uploadSuccess && (
        <Alert className="mb-6 border-green-200 bg-green-50">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            {uploadSuccess}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-8">
        <div className="bg-muted/30 rounded-lg p-6">
          <div className="text-center space-y-4">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={handleFileChange}
              accept={SUPPORTED_FILE_TYPES.join(',')}
            />

            <Button
              variant="outline"
              onClick={triggerFileInput}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Choose Files
            </Button>

            <div className="text-sm text-muted-foreground space-y-2">
              <p>Upload any relevant documents, plans, or diagrams</p>
              <p className="text-xs">
                Supported: PDF, DOC, TXT, Images (JPG, PNG), CAD files (DWG, DXF), Excel, ZIP
              </p>
              <p className="text-xs">Maximum file size: 10MB per file</p>
            </div>
          </div>

          {uploadedFiles.length > 0 && (
            <div className="mt-6">
              <Label className="mb-2 block">Uploaded Files ({uploadedFiles.length})</Label>
              <div className="space-y-2">
                {uploadedFiles.map((file, index) => {
                  const formatFileSize = (bytes: number) => {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                  };

                  return (
                    <div key={index} className="flex justify-between items-center bg-background p-3 rounded border">
                      <div className="flex-1 min-w-0 mr-3">
                        <div className="text-sm font-medium truncate">{file.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatFileSize(file.size)} • {file.type || 'Unknown type'}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="h-8 w-8 p-0 flex-shrink-0"
                        title="Remove file"
                      >
                        <FileX className="h-4 w-4" />
                        <span className="sr-only">Remove file</span>
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      <FormNavigation />
    </div>
  );
};

export default FilesUploadStep;
