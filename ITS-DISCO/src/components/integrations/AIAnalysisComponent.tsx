import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Zap, Key } from 'lucide-react';
import { useFormContext } from '../../context/FormContext';
import { generateRecommendations, getOpenAIKey, OpenAIResponse } from '@/services/openaiService';
import { Card, CardContent } from '@/components/ui/card';

interface AIAnalysisComponentProps {
  analysisType: 'system-recommendations' | 'summary-report' | 'budget-analysis';
}

const AIAnalysisComponent: React.FC<AIAnalysisComponentProps> = ({ analysisType }) => {
  const { formData } = useFormContext();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [result, setResult] = useState<string>('');

  // Get API key from settings instead of local state
  const apiKey = getOpenAIKey();



  const getSystemPrompt = (): string => {
    switch(analysisType) {
      case 'system-recommendations':
        return "You are an expert home automation and AV systems consultant. Provide specific product recommendations and system design suggestions based on the client's needs and room configuration.";
      case 'summary-report':
        return "You are a professional technical writer for a high-end home automation company. Create a concise yet comprehensive summary of the client walkthrough information that can be presented to the client.";
      case 'budget-analysis':
        return "You are a financial analyst specializing in home automation and AV systems. Analyze the client's requirements and provide a budget breakdown with cost ranges for each system component.";
      default:
        return "You are a home automation and AV systems expert. Provide helpful insights based on the client walkthrough information.";
    }
  };

  const getUserPrompt = (): string => {
    const { clientInfo, qualificationInfo, systemCategories, rooms } = formData;

    const selectedSystems = Object.entries(systemCategories)
      .filter(([_, value]) => value)
      .map(([key]) => key);

    // Create a detailed prompt based on the analysis type
    switch(analysisType) {
      case 'system-recommendations':
        return `I need specific product recommendations and system design suggestions for a client with the following details:

Client: ${clientInfo.fullName}
Project Type: ${clientInfo.projectType}
Home Type: ${clientInfo.homeType}
Budget Range: $${qualificationInfo.budget.toLocaleString()}
Square Footage: ${qualificationInfo.squareFootage.toLocaleString()} sq ft
Number of Floors: ${qualificationInfo.floors}
Project Stage: ${qualificationInfo.projectStage}
Target Completion: ${formatDate(qualificationInfo.completionDate)}

Selected Systems: ${selectedSystems.join(', ')}

Room Details:
${rooms.map(room => `
- ${room.name}:
  ${room.lighting ? '✓ Lighting Control' : ''}
  ${room.shades !== 'none' ? `✓ ${room.shades} Shades` : ''}
  ${room.audio !== 'none' ? `✓ ${room.audio} Audio` : ''}
  ${room.video !== 'none' ? `✓ ${room.video} Video` : ''}
  ${room.surveillance ? '✓ Surveillance' : ''}
  ${room.control !== 'none' ? `✓ ${room.control} Control` : ''}
  ${room.notes ? `Notes: ${room.notes}` : ''}
`).join('')}

Please provide detailed product recommendations and system design suggestions for this client.`;

      case 'summary-report':
        return `Generate a professional executive summary of the following home automation walkthrough for a high-end client:

Client: ${clientInfo.fullName}
Project Address: ${clientInfo.address}
Project Type: ${clientInfo.projectType}
Home Type: ${clientInfo.homeType}
Budget Range: $${qualificationInfo.budget.toLocaleString()}
Square Footage: ${qualificationInfo.squareFootage.toLocaleString()} sq ft
Number of Floors: ${qualificationInfo.floors}
Project Stage: ${qualificationInfo.projectStage}
Target Completion: ${formatDate(qualificationInfo.completionDate)}
${clientInfo.architectInfo ? `Architect/Designer: ${clientInfo.architectInfo}` : ''}

Selected Systems: ${selectedSystems.join(', ')}

Room Details:
${rooms.map(room => `
- ${room.name}:
  ${room.lighting ? '✓ Lighting Control' : ''}
  ${room.shades !== 'none' ? `✓ ${room.shades} Shades` : ''}
  ${room.audio !== 'none' ? `✓ ${room.audio} Audio` : ''}
  ${room.video !== 'none' ? `✓ ${room.video} Video` : ''}
  ${room.surveillance ? '✓ Surveillance' : ''}
  ${room.control !== 'none' ? `✓ ${room.control} Control` : ''}
  ${room.notes ? `Notes: ${room.notes}` : ''}
`).join('')}

Format this as a professional executive summary that I can present to the client.`;

      case 'budget-analysis':
        return `Provide a budget analysis for a home automation project with the following details:

Client: ${clientInfo.fullName}
Project Type: ${clientInfo.projectType}
Home Type: ${clientInfo.homeType}
Current Budget: $${qualificationInfo.budget.toLocaleString()}
Square Footage: ${qualificationInfo.squareFootage.toLocaleString()} sq ft
Number of Floors: ${qualificationInfo.floors}
Project Stage: ${qualificationInfo.projectStage}

Selected Systems: ${selectedSystems.join(', ')}

Room Details:
${rooms.map(room => `
- ${room.name}:
  ${room.lighting ? '✓ Lighting Control' : ''}
  ${room.shades !== 'none' ? `✓ ${room.shades} Shades` : ''}
  ${room.audio !== 'none' ? `✓ ${room.audio} Audio` : ''}
  ${room.video !== 'none' ? `✓ ${room.video} Video` : ''}
  ${room.surveillance ? '✓ Surveillance' : ''}
  ${room.control !== 'none' ? `✓ ${room.control} Control` : ''}
  ${room.notes ? `Notes: ${room.notes}` : ''}
`).join('')}

Please provide a budget breakdown with estimated cost ranges for each system component.`;

      default:
        return `Analyze this home automation client walkthrough and provide insights:

Client: ${clientInfo.fullName}
Project Type: ${clientInfo.projectType}
Budget Range: $${qualificationInfo.budget.toLocaleString()}
Selected Systems: ${selectedSystems.join(', ')}
Number of Rooms: ${rooms.length}`;
    }
  };

  const generateAnalysis = async () => {
    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Please configure your OpenAI API key in Settings first",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setResult('');

    try {
      const systemPrompt = getSystemPrompt();
      const userPrompt = getUserPrompt();

      const response: OpenAIResponse = await generateRecommendations(
        systemPrompt,
        userPrompt,
        apiKey
      );

      if (response.success) {
        setResult(response.content);
        toast({
          title: "Analysis Complete",
          description: "AI analysis has been generated successfully",
        });
      } else {
        throw new Error(response.error || "Failed to generate analysis");
      }
    } catch (error) {
      console.error("Error generating analysis:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate analysis. Please check your API key in Settings and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {!apiKey && (
        <div className="p-4 bg-muted/50 rounded-lg border border-dashed">
          <p className="text-sm text-muted-foreground text-center">
            <Key className="h-4 w-4 inline mr-2" />
            OpenAI API key required. Please configure it in{' '}
            <a href="/settings" className="text-primary hover:underline font-medium">
              Settings
            </a>{' '}
            to generate AI analysis.
          </p>
        </div>
      )}

      <Button
        onClick={generateAnalysis}
        disabled={isLoading}
        className="w-full bg-[#37372f] hover:bg-[#4a4a3f] text-white"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          <>
            <Zap className="mr-2 h-4 w-4" />
            Generate AI Analysis
          </>
        )}
      </Button>

      {result && (
        <Card className="mt-6">
          <CardContent className="pt-6">
            <Textarea
              value={result}
              readOnly
              className="min-h-[300px] font-serif text-sm"
            />
            <div className="flex justify-end mt-4">
              <Button
                variant="outline"
                onClick={() => {
                  navigator.clipboard.writeText(result);
                  toast({
                    title: "Copied",
                    description: "Analysis copied to clipboard",
                  });
                }}
              >
                Copy to Clipboard
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AIAnalysisComponent;

// Helper function to format dates safely
const formatDate = (date: Date | string | null): string => {
  if (!date) return "Not specified";

  try {
    // Handle both string and Date objects
    if (typeof date === 'string') {
      return new Date(date).toLocaleDateString();
    } else if (date instanceof Date) {
      return date.toLocaleDateString();
    }
    return "Invalid date";
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Date format error";
  }
};
