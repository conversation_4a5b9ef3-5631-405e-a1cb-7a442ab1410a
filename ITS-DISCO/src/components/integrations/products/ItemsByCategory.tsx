
import React, { useState } from 'react';
import { ProductItem } from '@/hooks/usePackageManagement';
import { systemCategories } from '@/hooks/usePackageManagement';
import ItemCard from '../packages/ItemCard';
import { Button } from '@/components/ui/button';
import { Plus, Lightbulb, PanelTop, Speaker, Monitor, Wifi, ShieldCheck, Radio, ChevronDown, ChevronUp, ChevronsDown, ChevronsUp, Pencil, Trash2 } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';

interface ItemsByCategoryProps {
  categories: string[];
  items: ProductItem[];
  onAddItem: () => void;
  onEditItem: (itemId: string) => void;
  onDeleteItem: (itemId: string) => void;
  isStandalone?: boolean;
  viewMode?: 'card' | 'list';
  selectedItems?: string[];
  onSelectItem?: (itemId: string, isSelected: boolean) => void;
  bulkEditMode?: boolean;
}

// Compact list item component for list view
const ProductListItem: React.FC<{
  item: ProductItem;
  onEdit: () => void;
  onDelete: () => void;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
  bulkEditMode?: boolean;
}> = ({ item, onEdit, onDelete, selected = false, onSelect, bulkEditMode = false }) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const handleCardClick = () => {
    if (onSelect) {
      onSelect(!selected);
    }
  };

  return (
    <Card
      className={`
        transition-colors hover:bg-muted/20 cursor-pointer
        ${selected ? 'border-primary bg-primary/5' : ''}
      `}
      onClick={handleCardClick}
    >
      <CardContent className="p-3">
        <div className="flex items-center gap-3">
          {/* Checkbox for bulk edit mode */}
          {bulkEditMode && onSelect && (
            <Checkbox
              checked={selected}
              onCheckedChange={(checked) => onSelect(checked === true)}
              onClick={(e) => e.stopPropagation()}
            />
          )}

          {/* Small product image */}
          <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
            {item.imageUrl ? (
              <img
                src={item.imageUrl}
                alt={item.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg';
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <img
                  src="/placeholder.svg"
                  alt="No image"
                  className="w-6 h-6 opacity-50"
                />
              </div>
            )}
          </div>

          {/* Product info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-sm truncate">{item.name}</h3>
                <p className="text-xs text-muted-foreground truncate">
                  {item.manufacturer && `${item.manufacturer}`}
                  {item.model && item.manufacturer && ` - `}
                  {item.model && `${item.model}`}
                </p>
              </div>

              {/* Price and SKU */}
              <div className="text-right ml-3 flex-shrink-0">
                <p className="text-sm font-medium">{formatCurrency(item.unitCost)}</p>
                <p className="text-xs text-muted-foreground">SKU: {item.sku || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-1 flex-shrink-0">
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onEdit();
              }}
            >
              <Pencil className="h-3 w-3" />
            </Button>

            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ItemsByCategory: React.FC<ItemsByCategoryProps> = ({
  categories,
  items,
  onAddItem,
  onEditItem,
  onDeleteItem,
  isStandalone = false,
  viewMode = 'card',
  selectedItems = [],
  onSelectItem,
  bulkEditMode = false
}) => {
  // Track collapsed state for each category
  const [collapsedCategories, setCollapsedCategories] = useState<Record<string, boolean>>({});

  // Toggle collapse state for a category
  const toggleCategory = (category: string) => {
    setCollapsedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Toggle collapse for all categories
  const toggleAllCategories = (collapseAll: boolean) => {
    const newState: Record<string, boolean> = {};
    categories.forEach(category => {
      newState[category] = collapseAll;
    });
    setCollapsedCategories(newState);
  };

  // Get the category icon based on the category name
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'lighting': return <Lightbulb className="h-5 w-5 text-amber-500" />;
      case 'shades': return <PanelTop className="h-5 w-5 text-indigo-500" />;
      case 'audio': return <Speaker className="h-5 w-5 text-emerald-500" />;
      case 'video': return <Monitor className="h-5 w-5 text-blue-500" />;
      case 'network': return <Wifi className="h-5 w-5 text-sky-500" />;
      case 'security': return <ShieldCheck className="h-5 w-5 text-rose-500" />;
      case 'controlSystems': return <Radio className="h-5 w-5 text-purple-500" />;
      default: return null;
    }
  };

  // Get the label for a category
  const getCategoryLabel = (category: string): string => {
    return systemCategories[category as keyof typeof systemCategories] || category;
  };

  // Filter items by category
  const getItemsByCategory = (category: string) => {
    return items.filter(item => item.systemCategory === category);
  };

  // If no items, show empty state
  if (items.length === 0) {
    return (
      <div className="py-10 text-center">
        <h3 className="text-lg font-medium mb-2">No products found</h3>
        <p className="text-muted-foreground mb-4">Add your first product to get started.</p>
        <Button onClick={onAddItem}>
          <Plus className="mr-2 h-4 w-4" />
          Add Product
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8 mb-10">
      {items.length > 0 && (
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => toggleAllCategories(Object.keys(collapsedCategories).some(cat => !collapsedCategories[cat]))}
            className="mb-2"
          >
            {Object.keys(collapsedCategories).some(cat => !collapsedCategories[cat]) ? (
              <>
                <ChevronsUp className="mr-2 h-4 w-4" />
                Collapse All
              </>
            ) : (
              <>
                <ChevronsDown className="mr-2 h-4 w-4" />
                Expand All
              </>
            )}
          </Button>
        </div>
      )}

      {categories.map(category => {
        const categoryItems = getItemsByCategory(category);

        if (categoryItems.length === 0) return null;

        const isCollapsed = collapsedCategories[category];

        return (
          <Collapsible
            key={category}
            open={!isCollapsed}
            className="border rounded-lg overflow-hidden"
          >
            <CollapsibleTrigger
              asChild
              onClick={(e) => {
                e.preventDefault();
                toggleCategory(category);
              }}
            >
              <div className="flex items-center justify-between p-4 bg-muted/20 hover:bg-muted/30 cursor-pointer">
                <div className="flex items-center gap-2">
                  {getCategoryIcon(category)}
                  <h2 className="text-xl font-semibold">{getCategoryLabel(category)}</h2>
                  <span className="text-muted-foreground ml-2">({categoryItems.length})</span>
                </div>
                {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
              </div>
            </CollapsibleTrigger>

            <CollapsibleContent>
              <div className="p-4">
                {viewMode === 'card' ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {categoryItems.map(item => (
                      <ItemCard
                        key={item.id}
                        item={item}
                        onEdit={() => onEditItem(item.id)}
                        onDelete={() => onDeleteItem(item.id)}
                        isStandalone={isStandalone}
                        selected={selectedItems?.includes(item.id)}
                        onSelect={bulkEditMode && onSelectItem ?
                          (selected) => onSelectItem(item.id, selected) :
                          undefined}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {categoryItems.map(item => (
                      <ProductListItem
                        key={item.id}
                        item={item}
                        onEdit={() => onEditItem(item.id)}
                        onDelete={() => onDeleteItem(item.id)}
                        selected={selectedItems?.includes(item.id)}
                        onSelect={bulkEditMode && onSelectItem ?
                          (selected) => onSelectItem(item.id, selected) :
                          undefined}
                        bulkEditMode={bulkEditMode}
                      />
                    ))}
                  </div>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>
        );
      })}
    </div>
  );
};

export default ItemsByCategory;
