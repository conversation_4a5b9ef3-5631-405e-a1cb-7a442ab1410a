-- Reference Quote Learning System Database Tables
-- Run this SQL in your Supabase SQL editor to create the necessary tables

-- Create reference_quotes table
CREATE TABLE IF NOT EXISTS reference_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    extracted_data JSONB,
    ai_analysis JSONB,
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    room_count INTEGER,
    square_footage INTEGER,
    complexity_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    tags TEXT[],
    team_id UUID REFERENCES teams(id),
    uploaded_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create ai_recommendations table
CREATE TABLE IF NOT EXISTS ai_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    walkthrough_id UUID REFERENCES walkthroughs(id) NOT NULL,
    recommendation_type TEXT NOT NULL CHECK (recommendation_type IN ('system_suggestion', 'product_combination', 'cost_optimization', 'configuration_option')),
    step_context TEXT NOT NULL,
    room_context TEXT,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    source_quotes TEXT[],
    reasoning TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'dismissed', 'modified')),
    user_feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create storage bucket for reference quotes
INSERT INTO storage.buckets (id, name, public) 
VALUES ('reference-quotes', 'reference-quotes', false)
ON CONFLICT (id) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reference_quotes_processing_status ON reference_quotes(processing_status);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_project_type ON reference_quotes(project_type);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_system_categories ON reference_quotes USING GIN(system_categories);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_tags ON reference_quotes USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_team_id ON reference_quotes(team_id);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_uploaded_by ON reference_quotes(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_reference_quotes_created_at ON reference_quotes(created_at);

CREATE INDEX IF NOT EXISTS idx_ai_recommendations_walkthrough_id ON ai_recommendations(walkthrough_id);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(recommendation_type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_step_context ON ai_recommendations(step_context);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_confidence ON ai_recommendations(confidence_score);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_reference_quotes_updated_at 
    BEFORE UPDATE ON reference_quotes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_recommendations_updated_at 
    BEFORE UPDATE ON ai_recommendations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE reference_quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;

-- RLS policies for reference_quotes
CREATE POLICY "Users can view their team's reference quotes" ON reference_quotes
    FOR SELECT USING (
        team_id IN (
            SELECT team_id FROM profiles WHERE id = auth.uid()
        ) OR uploaded_by = auth.uid()
    );

CREATE POLICY "Users can insert reference quotes for their team" ON reference_quotes
    FOR INSERT WITH CHECK (
        uploaded_by = auth.uid() AND
        (team_id IS NULL OR team_id IN (
            SELECT team_id FROM profiles WHERE id = auth.uid()
        ))
    );

CREATE POLICY "Users can update their own reference quotes" ON reference_quotes
    FOR UPDATE USING (uploaded_by = auth.uid());

CREATE POLICY "Users can delete their own reference quotes" ON reference_quotes
    FOR DELETE USING (uploaded_by = auth.uid());

-- RLS policies for ai_recommendations
CREATE POLICY "Users can view recommendations for their walkthroughs" ON ai_recommendations
    FOR SELECT USING (
        walkthrough_id IN (
            SELECT id FROM walkthroughs WHERE created_by = auth.uid()
        )
    );

CREATE POLICY "System can insert recommendations" ON ai_recommendations
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update recommendations for their walkthroughs" ON ai_recommendations
    FOR UPDATE USING (
        walkthrough_id IN (
            SELECT id FROM walkthroughs WHERE created_by = auth.uid()
        )
    );

-- Storage policies for reference-quotes bucket
CREATE POLICY "Users can upload reference quote files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'reference-quotes' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their reference quote files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'reference-quotes' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their reference quote files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'reference-quotes' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Grant necessary permissions
GRANT ALL ON reference_quotes TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- Create helpful views for analytics
CREATE OR REPLACE VIEW reference_quote_analytics AS
SELECT 
    COUNT(*) as total_quotes,
    COUNT(*) FILTER (WHERE processing_status = 'completed') as processed_quotes,
    COUNT(*) FILTER (WHERE processing_status = 'failed') as failed_quotes,
    COUNT(*) FILTER (WHERE processing_status = 'pending') as pending_quotes,
    AVG(estimated_budget) FILTER (WHERE estimated_budget IS NOT NULL) as avg_budget,
    COUNT(DISTINCT project_type) as unique_project_types,
    COUNT(DISTINCT uploaded_by) as unique_uploaders,
    DATE_TRUNC('month', created_at) as month
FROM reference_quotes
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month DESC;

CREATE OR REPLACE VIEW ai_recommendation_analytics AS
SELECT 
    recommendation_type,
    COUNT(*) as total_recommendations,
    COUNT(*) FILTER (WHERE status = 'accepted') as accepted_count,
    COUNT(*) FILTER (WHERE status = 'dismissed') as dismissed_count,
    AVG(confidence_score) as avg_confidence,
    DATE_TRUNC('day', created_at) as day
FROM ai_recommendations
GROUP BY recommendation_type, DATE_TRUNC('day', created_at)
ORDER BY day DESC, recommendation_type;

-- Comments for documentation
COMMENT ON TABLE reference_quotes IS 'Stores uploaded reference quotes for AI analysis and learning';
COMMENT ON TABLE ai_recommendations IS 'Stores AI-generated recommendations for walkthroughs based on reference quote analysis';
COMMENT ON COLUMN reference_quotes.extracted_data IS 'Raw extracted data from the quote file (text, line items, etc.)';
COMMENT ON COLUMN reference_quotes.ai_analysis IS 'AI-generated analysis including patterns, recommendations, and benchmarks';
COMMENT ON COLUMN ai_recommendations.recommendation_data IS 'Structured recommendation data including title, description, benefits, etc.';
COMMENT ON COLUMN ai_recommendations.source_quotes IS 'Array of reference quote IDs that influenced this recommendation';

-- Success message
SELECT 'Reference Quote Learning System tables created successfully!' as message;
