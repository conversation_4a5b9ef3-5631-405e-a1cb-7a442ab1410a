# Reference Quote Learning System

## Overview

The Reference Quote Learning System is an AI-powered enhancement to the ITS Discovery application that learns from historical project quotes to provide intelligent recommendations during client walkthroughs. The system analyzes uploaded reference quotes to identify patterns, suggest system combinations, optimize costs, and improve project estimation accuracy.

## Key Features

### 🧠 AI-Powered Analysis
- **Multi-Format Support**: Upload quotes in PDF, Excel, CSV, Word, and image formats
- **Intelligent Extraction**: Automatically extracts project details, costs, and system configurations
- **Pattern Recognition**: Identifies common system combinations and pricing patterns
- **Continuous Learning**: Improves recommendations as more quotes are uploaded

### 📊 Real-Time Recommendations
- **Contextual Suggestions**: Provides relevant recommendations based on current walkthrough context
- **System Combinations**: Suggests complementary systems based on historical data
- **Cost Optimization**: Identifies potential savings and alternative configurations
- **Confidence Scoring**: Shows reliability of each recommendation with confidence percentages

### 💰 Enhanced Cost Estimation
- **Historical Benchmarking**: Compares current estimates with similar past projects
- **Budget Validation**: Flags potential cost overruns or underestimates
- **Alternative Pricing**: Suggests different price points based on historical data
- **Labor vs Materials Analysis**: Provides insights into cost breakdowns

## System Architecture

### Database Schema

#### Reference Quotes Table
```sql
reference_quotes (
    id UUID PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    processing_status TEXT, -- 'pending', 'processing', 'completed', 'failed'
    extracted_data JSONB,   -- Raw extracted data
    ai_analysis JSONB,      -- AI-generated insights
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    confidence_score DECIMAL(3,2),
    team_id UUID,
    uploaded_by UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
```

#### AI Recommendations Table
```sql
ai_recommendations (
    id UUID PRIMARY KEY,
    walkthrough_id UUID,
    recommendation_type TEXT, -- 'system_suggestion', 'product_combination', 'cost_optimization'
    step_context TEXT,
    room_context TEXT,
    recommendation_data JSONB,
    confidence_score DECIMAL(3,2),
    source_quotes TEXT[],
    reasoning TEXT,
    status TEXT, -- 'pending', 'accepted', 'dismissed', 'modified'
    user_feedback TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
```

### File Processing Pipeline

1. **Upload**: Files are uploaded to Supabase Storage with validation
2. **Extraction**: Data is extracted based on file type:
   - **PDF**: Text extraction using PDF.js
   - **Excel/CSV**: Structured data parsing
   - **Images**: OCR processing for text recognition
   - **Word**: Document text extraction
3. **AI Analysis**: OpenAI processes extracted data to identify patterns
4. **Storage**: Results are stored in the database for future recommendations

### Recommendation Engine

The AI recommendation engine operates in real-time during walkthroughs:

1. **Context Analysis**: Analyzes current walkthrough state (step, room, selections)
2. **Quote Matching**: Finds relevant historical quotes based on:
   - Project type and budget
   - Selected systems
   - Room configurations
   - Square footage
3. **Pattern Recognition**: Identifies relevant patterns from matched quotes
4. **Recommendation Generation**: Creates contextual suggestions with confidence scores
5. **User Feedback**: Learns from user acceptance/dismissal of recommendations

## User Interface Components

### Reference Quote Upload
- **Multi-file selection** with drag-and-drop support
- **File validation** for supported formats and size limits
- **Progress tracking** for upload and processing status
- **Metadata entry** for project type, tags, and notes

### AI Recommendation Panel
- **Contextual display** during walkthrough steps
- **Expandable cards** with detailed recommendation information
- **Confidence indicators** showing reliability of suggestions
- **User feedback** collection for continuous improvement
- **Accept/Dismiss actions** with optional feedback

### Quote Management Interface
- **Search and filtering** by project type, budget, systems
- **Processing status** tracking and error handling
- **Analytics dashboard** showing system performance
- **Bulk operations** for managing multiple quotes

## Integration Points

### Walkthrough Process
The system integrates seamlessly into existing walkthrough steps:

- **System Selection**: Suggests additional systems based on patterns
- **Room Configuration**: Recommends typical room setups
- **Cost Estimation**: Provides budget validation and alternatives
- **Final Review**: Offers optimization suggestions

### Settings Integration
- **Data Management**: Accessible from Settings > Data Management
- **Configuration**: Upload preferences and processing options
- **Analytics**: Performance metrics and learning insights

## Setup Instructions

### 1. Database Setup
Run the SQL migration script to create necessary tables:
```bash
# Execute in Supabase SQL Editor
src/utils/createReferenceQuoteTables.sql
```

### 2. Storage Configuration
The system automatically creates a `reference-quotes` storage bucket with appropriate security policies.

### 3. OpenAI Configuration
Ensure OpenAI API key is configured in Settings > AI / OpenAI for:
- Quote analysis and pattern recognition
- Recommendation generation
- Cost optimization suggestions

### 4. File Upload Limits
Configure appropriate file size limits in your hosting environment:
- **Default**: 50MB per file
- **Recommended**: Adjust based on typical quote file sizes

## Usage Guide

### Uploading Reference Quotes

1. **Navigate** to Settings > Data Management > Reference Quote Learning System
2. **Click** "Manage" to open the Reference Quote Manager
3. **Select** the "Upload Quotes" tab
4. **Choose files** or drag and drop quote documents
5. **Enter metadata**:
   - Title (required)
   - Project type
   - Tags for organization
   - Additional notes
6. **Click** "Upload & Analyze" to begin processing

### Managing Quotes

- **View** all uploaded quotes with processing status
- **Search** by title, project type, or content
- **Filter** by processing status, budget range, or date
- **Delete** quotes that are no longer needed
- **Monitor** processing progress and errors

### Using Recommendations

During walkthroughs, AI recommendations appear automatically:

1. **Review** suggestions in the recommendation panel
2. **Expand** cards for detailed information
3. **Check** confidence scores and reasoning
4. **Accept** recommendations to apply suggestions
5. **Dismiss** irrelevant recommendations
6. **Provide feedback** to improve future suggestions

## Best Practices

### Quote Selection
- **Upload diverse projects** to improve pattern recognition
- **Include recent quotes** for current market pricing
- **Organize with tags** for better categorization
- **Maintain quality** by removing outdated or irrelevant quotes

### Recommendation Usage
- **Review confidence scores** before accepting suggestions
- **Provide feedback** to improve system learning
- **Consider context** - recommendations are suggestions, not requirements
- **Monitor patterns** to identify recurring suggestions

### System Maintenance
- **Regular cleanup** of old or irrelevant quotes
- **Monitor processing** for failed uploads
- **Review analytics** to understand system performance
- **Update configurations** based on usage patterns

## Security and Privacy

### Data Protection
- **Row Level Security** ensures users only access their team's data
- **Encrypted storage** for all uploaded files
- **Secure processing** with no data retention in external services
- **Access controls** based on team membership

### File Security
- **Virus scanning** (recommended for production)
- **File type validation** to prevent malicious uploads
- **Size limits** to prevent abuse
- **Automatic cleanup** of temporary processing files

## Troubleshooting

### Common Issues

**Upload Failures**
- Check file size limits (50MB default)
- Verify file format is supported
- Ensure stable internet connection
- Check browser console for errors

**Processing Errors**
- Verify OpenAI API key is configured
- Check file content is readable
- Review error messages in quote management
- Contact support for persistent issues

**Missing Recommendations**
- Ensure sufficient reference quotes are uploaded
- Check walkthrough context matches uploaded quotes
- Verify OpenAI API key has sufficient credits
- Review confidence threshold settings

### Performance Optimization

- **Batch uploads** for multiple files
- **Regular maintenance** to remove unused quotes
- **Monitor storage usage** and clean up as needed
- **Optimize file sizes** before uploading

## Future Enhancements

### Planned Features
- **Advanced OCR** for better image processing
- **Machine learning models** for improved pattern recognition
- **Integration APIs** for external quote systems
- **Automated categorization** of uploaded quotes
- **Predictive analytics** for market trends

### Extensibility
The system is designed for easy extension:
- **Plugin architecture** for new file formats
- **API endpoints** for external integrations
- **Configurable processing** pipelines
- **Custom recommendation** types

## Support

For technical support or feature requests:
- **Documentation**: Review this guide and API documentation
- **Issues**: Create GitHub issues for bugs or feature requests
- **Contact**: <EMAIL> for direct support

---

*The Reference Quote Learning System represents a significant advancement in AI-powered project estimation and recommendation technology for the home automation industry.*
