# Digital Ocean App Platform configuration
name: its-discovery
services:
- name: web
  source_dir: /
  github:
    repo: your-username/its-discovery
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  http_port: 8080
  routes:
  - path: /
  health_check:
    http_path: /
  envs:
  - key: NODE_ENV
    value: production
  - key: VITE_SUPABASE_URL
    value: https://nlfmaapgqvomssugaxzw.supabase.co
  - key: VITE_SUPABASE_ANON_KEY
    value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Em3zzRB6-PyDae2rbL1XR6m0UteYzNWKfsBp_CVzWS0
