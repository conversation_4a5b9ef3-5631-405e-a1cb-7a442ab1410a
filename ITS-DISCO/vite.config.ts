import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/

export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    allowedHosts: ['disco-n5et.onrender.com'], // ✅ Add this line
  },
  plugins: [
    react(),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // React ecosystem
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],

          // UI libraries
          'ui-vendor': [
            '@radix-ui/react-accordion',
            '@radix-ui/react-alert-dialog',
            '@radix-ui/react-aspect-ratio',
            '@radix-ui/react-avatar',
            '@radix-ui/react-checkbox',
            '@radix-ui/react-collapsible',
            '@radix-ui/react-context-menu',
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-hover-card',
            '@radix-ui/react-label',
            '@radix-ui/react-menubar',
            '@radix-ui/react-navigation-menu',
            '@radix-ui/react-popover',
            '@radix-ui/react-progress',
            '@radix-ui/react-radio-group',
            '@radix-ui/react-scroll-area',
            '@radix-ui/react-select',
            '@radix-ui/react-separator',
            '@radix-ui/react-slider',
            '@radix-ui/react-slot',
            '@radix-ui/react-switch',
            '@radix-ui/react-tabs',
            '@radix-ui/react-toast',
            '@radix-ui/react-toggle',
            '@radix-ui/react-toggle-group',
            '@radix-ui/react-tooltip'
          ],

          // Data and state management
          'data-vendor': [
            '@tanstack/react-query',
            '@tanstack/react-table',
            'react-hook-form',
            '@hookform/resolvers',
            'zod'
          ],

          // Supabase and backend
          'backend-vendor': ['@supabase/supabase-js', 'axios'],

          // Utility libraries
          'utils-vendor': [
            'date-fns',
            'uuid',
            'clsx',
            'class-variance-authority',
            'tailwind-merge'
          ],

          // Chart and visualization
          'charts-vendor': ['recharts'],

          // PDF and document generation
          'pdf-vendor': ['jspdf', 'jspdf-autotable'],

          // Icons and UI components
          'icons-vendor': ['lucide-react'],

          // Other UI components
          'components-vendor': [
            'cmdk',
            'embla-carousel-react',
            'input-otp',
            'next-themes',
            'react-day-picker',
            'react-resizable-panels',
            'sonner',
            'vaul'
          ]
        }
      }
    },
    // Increase chunk size warning limit to 1MB
    chunkSizeWarningLimit: 1000
  }
}));
