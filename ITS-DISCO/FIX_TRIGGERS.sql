-- 🔧 FIX TRIGGER ERRORS - Safe Trigger Recreation
-- Run this script to fix the trigger already exists error
-- This script can be run multiple times safely

-- Use a transaction block to handle errors gracefully
DO $$
BEGIN
    -- Drop existing triggers if they exist
    DROP TRIGGER IF EXISTS update_reference_quotes_updated_at ON reference_quotes;
    DROP TRIGGER IF EXISTS update_ai_recommendations_updated_at ON ai_recommendations;

    -- Wait a moment to ensure drops are processed
    PERFORM pg_sleep(0.1);

    -- Create or replace the updated_at function
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $func$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $func$ language 'plpgsql';

    -- Recreate triggers with error handling
    BEGIN
        CREATE TRIGGER update_reference_quotes_updated_at
            BEFORE UPDATE ON reference_quotes
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    EXCEPTION
        WHEN duplicate_object THEN
            RAISE NOTICE 'Trigger update_reference_quotes_updated_at already exists, skipping';
    END;

    BEGIN
        CREATE TRIGGER update_ai_recommendations_updated_at
            BEFORE UPDATE ON ai_recommendations
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    EXCEPTION
        WHEN duplicate_object THEN
            RAISE NOTICE 'Trigger update_ai_recommendations_updated_at already exists, skipping';
    END;

END $$;

-- Success message
SELECT 'Triggers fixed successfully! ✅' as message,
       'You can now run other setup scripts without trigger errors.' as details;
