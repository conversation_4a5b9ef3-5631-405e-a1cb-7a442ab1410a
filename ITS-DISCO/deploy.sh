#!/bin/bash

# ITS Discovery Deployment Script for Digital Ocean

echo "🚀 Starting ITS Discovery deployment..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Run type checking
echo "🔍 Running type checks..."
npm run type-check

# Run linting
echo "🧹 Running linter..."
npm run lint

# Build the application
echo "🏗️ Building application for production..."
npm run build:prod

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed - dist directory not found"
    exit 1
fi

echo "✅ Build completed successfully!"
echo "📁 Build output is in the 'dist' directory"
echo "🌐 Ready for deployment to Digital Ocean"

# Optional: Test the build locally
echo "🧪 Testing build locally..."
npm run preview &
PREVIEW_PID=$!

# Wait a moment for the server to start
sleep 3

# Check if the server is running
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ Local preview server is running at http://localhost:8080"
else
    echo "⚠️ Local preview server may not be running properly"
fi

# Kill the preview server
kill $PREVIEW_PID 2>/dev/null

echo "🎉 Deployment preparation complete!"
echo ""
echo "Next steps for Digital Ocean:"
echo "1. Push your code to GitHub"
echo "2. Connect your GitHub repo to Digital Ocean App Platform"
echo "3. Use the app.yaml configuration provided"
echo "4. Deploy!"
