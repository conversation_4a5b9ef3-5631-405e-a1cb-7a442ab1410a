-- 🗑️ REMOVE ALL TRIGGERS - Nuclear Option
-- This script removes all triggers and functions that could cause conflicts
-- Run this first, then try your other setup scripts

-- Remove all possible trigger variations
DROP TRIGGER IF EXISTS update_reference_quotes_updated_at ON reference_quotes CASCADE;
DROP TRIGGER IF EXISTS update_ai_recommendations_updated_at ON ai_recommendations CASCADE;
DROP TRIGGER IF EXISTS reference_quotes_updated_at ON reference_quotes CASCADE;
DROP TRIGGER IF EXISTS ai_recommendations_updated_at ON ai_recommendations CASCADE;

-- Remove the function that triggers use
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at() CASCADE;

-- Check what triggers still exist (should be empty)
SELECT 'Remaining triggers on reference_quotes:' as info,
       tgname as trigger_name
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
WHERE c.relname = 'reference_quotes'
  AND NOT tgisinternal;

SELECT 'Remaining triggers on ai_recommendations:' as info,
       tgname as trigger_name  
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
WHERE c.relname = 'ai_recommendations'
  AND NOT tgisinternal;

-- Success message
SELECT 'All triggers removed! 🧹' as status,
       'You can now run setup scripts without trigger conflicts.' as message;
