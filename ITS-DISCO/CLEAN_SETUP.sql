-- 🧹 CLEAN SETUP - Remove All Triggers First, Then Fresh Setup
-- This script first removes all existing triggers to prevent conflicts

-- STEP 1: Remove all existing triggers and functions
DROP TRIGGER IF EXISTS update_reference_quotes_updated_at ON reference_quotes;
DROP TRIGGER IF EXISTS update_ai_recommendations_updated_at ON ai_recommendations;
DROP FUNCTION IF EXISTS update_updated_at_column();

-- STEP 2: Drop and recreate tables to ensure clean state
DROP TABLE IF EXISTS ai_recommendations CASCADE;
DROP TABLE IF EXISTS reference_quotes CASCADE;

-- STEP 3: Create reference_quotes table (fresh)
CREATE TABLE reference_quotes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMPTZ DEFAULT NOW(),
    processed_date TIMESTAMPTZ,
    processing_status TEXT DEFAULT 'pending',
    extracted_data JSONB,
    ai_analysis JSONB,
    project_type TEXT,
    estimated_budget DECIMAL(12,2),
    system_categories TEXT[],
    room_count INTEGER,
    square_footage INTEGER,
    complexity_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    tags TEXT[],
    team_id UUID,
    uploaded_by UUID NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- STEP 4: Create ai_recommendations table (fresh)
CREATE TABLE ai_recommendations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    walkthrough_id UUID NOT NULL,
    recommendation_type TEXT NOT NULL,
    step_context TEXT NOT NULL,
    room_context TEXT,
    recommendation_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,
    source_quotes TEXT[],
    reasoning TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    user_feedback TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- STEP 5: Create storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('reference-quotes', 'reference-quotes', false)
ON CONFLICT (id) DO NOTHING;

-- STEP 6: Create essential indexes
CREATE INDEX idx_reference_quotes_processing_status ON reference_quotes(processing_status);
CREATE INDEX idx_reference_quotes_created_at ON reference_quotes(created_at);
CREATE INDEX idx_ai_recommendations_walkthrough_id ON ai_recommendations(walkthrough_id);

-- STEP 7: DISABLE RLS completely
ALTER TABLE reference_quotes DISABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations DISABLE ROW LEVEL SECURITY;

-- STEP 8: Grant broad permissions
GRANT ALL ON reference_quotes TO authenticated;
GRANT ALL ON ai_recommendations TO authenticated;
GRANT ALL ON reference_quotes TO anon;
GRANT ALL ON ai_recommendations TO anon;

-- STEP 9: Remove any existing storage policies and create new one
DROP POLICY IF EXISTS "Allow all operations on reference-quotes" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload reference quote files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view reference quote files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete reference quote files" ON storage.objects;

CREATE POLICY "Allow all operations on reference-quotes" ON storage.objects
    FOR ALL USING (bucket_id = 'reference-quotes');

-- STEP 10: Test the setup with a simple insert
INSERT INTO reference_quotes (title, file_name, file_path, file_type, file_size, uploaded_by)
VALUES ('Test Quote', 'test.pdf', 'test/test.pdf', 'application/pdf', 1024, '00000000-0000-0000-0000-000000000000');

-- Verify it worked
SELECT 'Clean setup completed successfully! 🎉' as status,
       COUNT(*) as total_quotes,
       'All triggers removed, fresh tables created!' as message
FROM reference_quotes;

-- Clean up test data
DELETE FROM reference_quotes WHERE title = 'Test Quote';

-- Final verification
SELECT 'Database is clean and ready! ✅' as result,
       'No triggers exist to cause conflicts.' as note,
       'Reference quote system is ready to use!' as instructions;
